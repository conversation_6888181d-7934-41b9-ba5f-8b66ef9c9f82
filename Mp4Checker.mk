# Makefile for Mp4Checker

CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -O2
TARGET = mp4checker
SOURCES = Mp4Checker.cpp
HEADERS = Mp4Checker.h

# 默认目标
all: $(TARGET)

# 编译目标
$(TARGET): $(SOURCES) $(HEADERS)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCES)

# 清理
clean:
	rm -f $(TARGET)

# 测试
test: $(TARGET)
	@echo "请提供MP4文件路径进行测试："
	@echo "./$(TARGET) <MP4文件路径> [-v]"

# 安装（可选）
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/

.PHONY: all clean test install
