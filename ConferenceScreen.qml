import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12

Rectangle {
    color: "white"
    
    Row {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20
        
        // Left side - User list
        Rectangle {
            width: parent.width * 0.6
            height: parent.height
            color: "transparent"
            
            Column {
                anchors.horizontalCenter: parent.horizontalCenter
                spacing: 30
                
                Text {
                    text: "会议参与者"
                    font.pointSize: 16
                    font.bold: true
                    anchors.horizontalCenter: parent.horizontalCenter
                }
                
                Grid {
                    anchors.horizontalCenter: parent.horizontalCenter
                    columns: 3
                    spacing: 30
                    
                    Repeater {
                        model: [
                            {name: "用户1", number: "7704..."},
                            {name: "用户2", number: "7704..."},
                            {name: "用户3", number: "7704..."},
                            {name: "用户4", number: "7704..."},
                            {name: "用户5", number: "7704..."},
                            {name: "用户6", number: "7704..."},
                            {name: "用户7", number: "7704..."},
                            {name: "用户8", number: "7704..."},
                            {name: "用户9", number: "7704..."}
                        ]
                        
                        Rectangle {
                            width: 100
                            height: 80
                            color: "#f5f5f5"
                            border.color: "#cccccc"
                            border.width: 1
                            radius: 5
                            
                            Column {
                                anchors.centerIn: parent
                                spacing: 5
                                
                                Text {
                                    text: modelData.name
                                    font.pointSize: 12
                                    font.bold: true
                                    anchors.horizontalCenter: parent.horizontalCenter
                                }
                                
                                Text {
                                    text: modelData.number
                                    font.pointSize: 10
                                    color: "#666666"
                                    anchors.horizontalCenter: parent.horizontalCenter
                                }
                            }
                        }
                    }
                }
            }
        }
        
        // Right side - Control buttons
        Rectangle {
            width: parent.width * 0.35
            height: parent.height
            color: "transparent"
            
            Grid {
                anchors.centerIn: parent
                columns: 2
                spacing: 15
                
                // Volume controls
                Button {
                    width: 120
                    height: 60
                    text: "音量-"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    width: 120
                    height: 60
                    text: "音量+"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                // Ring controls
                Button {
                    width: 120
                    height: 60
                    text: "铃声-"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    width: 120
                    height: 60
                    text: "铃声+"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                // Call controls
                Button {
                    width: 120
                    height: 60
                    text: "静默"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    width: 120
                    height: 60
                    text: "多方通话"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    width: 120
                    height: 60
                    text: "重拨"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    width: 120
                    height: 60
                    text: "转接"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    width: 120
                    height: 60
                    text: "免提"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    width: 120
                    height: 60
                    text: "话务台"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }
        }
    }
}