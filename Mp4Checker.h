#ifndef MP4_CHECKER_H
#define MP4_CHECKER_H

#include <string>
#include <cstdint>

/**
 * MP4文件完整性检查器
 * 通过解析MP4文件的box结构来验证文件是否完整
 */
class Mp4Checker {
public:
    /**
     * 检查MP4文件是否完整
     * @param filePath MP4文件路径
     * @return true表示文件完整，false表示文件损坏或不完整
     */
    static bool check(const std::string& filePath);
    
    /**
     * 检查MP4文件是否完整（详细版本，输出调试信息）
     * @param filePath MP4文件路径
     * @param verbose 是否输出详细信息
     * @return true表示文件完整，false表示文件损坏或不完整
     */
    static bool checkVerbose(const std::string& filePath, bool verbose = true);

private:
    /**
     * 从字节数组中读取32位大端序整数
     */
    static uint32_t readBigEndianUInt32(const uint8_t* buffer);
    
    /**
     * 从字节数组中读取64位大端序整数
     */
    static uint64_t readBigEndianUInt64(const uint8_t* buffer);
    
    /**
     * 获取box类型的字符串表示
     */
    static std::string getBoxType(const uint8_t* buffer);
};

#endif // MP4_CHECKER_H
