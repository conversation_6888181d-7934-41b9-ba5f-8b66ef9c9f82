import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12

Rectangle {
    color: "white"
    
    property string currentState: "dialing" // "dialing", "incoming", "active"
    property string phoneNumber: "15488564453"
    property string callDuration: "00:02"
    
    Row {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20
        
        // Left side - Number display and keypad
        Rectangle {
            width: parent.width * 0.6
            height: parent.height
            color: "transparent"
            
            Column {
                anchors.horizontalCenter: parent.horizontalCenter
                spacing: 20
                
                // Phone number display
                Rectangle {
                    width: 400
                    height: 60
                    color: "transparent"
                    
                    Row {
                        anchors.centerIn: parent
                        spacing: 10
                        
                        Text {
                            text: phoneNumber
                            font.pointSize: 24
                            color: currentState === "active" ? "#333333" : "#4CAF50"
                            anchors.verticalCenter: parent.verticalCenter
                        }
                        
                        Button {
                            width: 30
                            height: 30
                            text: "✕"
                            background: Rectangle {
                                color: "#cccccc"
                                radius: 15
                            }
                            contentItem: Text {
                                text: parent.text
                                color: "white"
                                horizontalAlignment: Text.AlignHCenter
                                verticalAlignment: Text.AlignVCenter
                            }
                        }
                    }
                }
                
                // Call status or avatar
                Rectangle {
                    width: 400
                    height: 200
                    color: "transparent"
                    anchors.horizontalCenter: parent.horizontalCenter
                    
                    Item {
                        anchors.centerIn: parent
                        width: 120
                        height: 120
                        
                        // Avatar for active call
                        Rectangle {
                            id: avatar
                            visible: currentState === "incoming" || currentState === "active"
                            anchors.centerIn: parent
                            width: 120
                            height: 120
                            color: "#00BCD4"
                            radius: 60
                            
                            Text {
                                anchors.centerIn: parent
                                text: "👤"
                                color: "white"
                                font.pointSize: 48
                            }
                        }
                    }
                    
                    // Call status text
                    Column {
                        anchors.horizontalCenter: parent.horizontalCenter
                        anchors.bottom: parent.bottom
                        spacing: 10
                        
                        Text {
                            text: currentState === "incoming" ? "呼入" : 
                                  currentState === "active" ? "通话中" : ""
                            color: "#00BCD4"
                            font.pointSize: 14
                            anchors.horizontalCenter: parent.horizontalCenter
                            visible: currentState !== "dialing"
                        }
                        
                        Text {
                            text: "8561"
                            font.pointSize: 20
                            anchors.horizontalCenter: parent.horizontalCenter
                            visible: currentState !== "dialing"
                        }
                        
                        Text {
                            text: currentState === "active" ? "通话时长 " + callDuration : ""
                            color: "#c41e3a"
                            font.pointSize: 12
                            anchors.horizontalCenter: parent.horizontalCenter
                            visible: currentState === "active"
                        }
                    }
                }
                
                // Keypad (visible in dialing mode)
                Grid {
                    visible: currentState === "dialing"
                    anchors.horizontalCenter: parent.horizontalCenter
                    columns: 3
                    spacing: 10
                    
                    Repeater {
                        model: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "*", "0", "#"]
                        
                        Button {
                            width: 80
                            height: 60
                            text: modelData
                            background: Rectangle {
                                color: "#f5f5f5"
                                border.color: "#cccccc"
                                border.width: 1
                                radius: 5
                            }
                            contentItem: Text {
                                text: parent.text
                                font.pointSize: 18
                                horizontalAlignment: Text.AlignHCenter
                                verticalAlignment: Text.AlignVCenter
                            }
                        }
                    }
                }
                
                // Call direction arrows (for incoming calls)
                Row {
                    visible: currentState === "incoming"
                    anchors.horizontalCenter: parent.horizontalCenter
                    spacing: 40
                    
                    Text {
                        text: "··· → 呼入 ← ···"
                        color: "#00BCD4"
                        font.pointSize: 14
                    }
                }
            }
        }
        
        // Right side - Control buttons
        Rectangle {
            width: parent.width * 0.35
            height: parent.height
            color: "transparent"
            
            Grid {
                anchors.centerIn: parent
                columns: 2
                spacing: 15
                
                // Volume controls
                Button {
                    width: 120
                    height: 60
                    text: "音量-"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    width: 120
                    height: 60
                    text: "音量+"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                // Ring controls
                Button {
                    width: 120
                    height: 60
                    text: "铃声-"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    width: 120
                    height: 60
                    text: "铃声+"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                // Call controls
                Button {
                    width: 120
                    height: 60
                    text: "静默"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    width: 120
                    height: 60
                    text: "多方通话"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    width: 120
                    height: 60
                    text: "重拨"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    width: 120
                    height: 60
                    text: "转接"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    width: 120
                    height: 60
                    text: "免提"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
                
                Button {
                    width: 120
                    height: 60
                    text: "话务台"
                    background: Rectangle {
                        color: "white"
                        border.color: "#4CAF50"
                        border.width: 2
                        radius: 5
                    }
                    contentItem: Text {
                        text: parent.text
                        color: "#4CAF50"
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }
        }
    }
    
    // State change buttons for demo
    Row {
        anchors.bottom: parent.bottom
        anchors.right: parent.right
        anchors.margins: 20
        spacing: 10
        
        Button {
            text: "拨号"
            onClicked: currentState = "dialing"
        }
        Button {
            text: "呼入"
            onClicked: currentState = "incoming"
        }
        Button {
            text: "通话"
            onClicked: currentState = "active"
        }
    }
}