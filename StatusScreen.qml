import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12

Rectangle {
    color: "white"
    
    Column {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20
        
        // Header
        Row {
            width: parent.width
            spacing: 20
            
            Text {
                text: "链路"
                font.pointSize: 14
                font.bold: true
                width: 80
            }
            
            Text {
                text: "运营商"
                font.pointSize: 14
                font.bold: true
                width: 120
            }
            
            Text {
                text: "信号"
                font.pointSize: 14
                font.bold: true
                width: 80
            }
            
            Text {
                text: "类别"
                font.pointSize: 14
                font.bold: true
                width: 80
            }
            
            Text {
                text: "上下行速率"
                font.pointSize: 14
                font.bold: true
                width: 200
            }
        }
        
        Rectangle {
            width: parent.width
            height: 1
            color: "#cccccc"
        }
        
        // Status list
        Column {
            width: parent.width
            spacing: 15
            
            Repeater {
                model: [
                    {slot: "卡槽1", carrier: "中国联通", signal: 4, type: "4G", upload: "256.0b", download: "1.8Kb", enabled: true},
                    {slot: "卡槽2", carrier: "中国移动", signal: 4, type: "4G", upload: "0.0b", download: "320.0b", enabled: true},
                    {slot: "卡槽3", carrier: "中国电信", signal: 4, type: "4G", upload: "0.0b", download: "1.8Kb", enabled: true},
                    {slot: "卡槽4", carrier: "中国联通", signal: 4, type: "4G", upload: "0.0b", download: "1.8Kb", enabled: true},
                    {slot: "卡槽5", carrier: "中国移动", signal: 3, type: "4G", upload: "0.0b", download: "320.0b", enabled: true},
                    {slot: "卡槽6", carrier: "—", signal: 0, type: "—", upload: "0.0b", download: "0.0b", enabled: false},
                    {slot: "卡槽7", carrier: "—", signal: 0, type: "—", upload: "0.0b", download: "0.0b", enabled: false},
                    {slot: "卡槽8", carrier: "天通卫星", signal: 0, type: "—", upload: "0.0b", download: "0.0b", enabled: false},
                    {slot: "网口1", carrier: "中星卫星", signal: 2, type: "—", upload: "0.0b", download: "0.0b", enabled: true},
                    {slot: "固定电话", carrier: "PSTN", signal: -1, type: "—", upload: "—", download: "—", enabled: true},
                    {slot: "固定电话", carrier: "PSTN", signal: -1, type: "—", upload: "—", download: "—", enabled: true},
                    {slot: "网口2", carrier: "—", signal: -1, type: "—", upload: "—", download: "—", enabled: true}
                ]
                
                Rectangle {
                    width: parent.width
                    height: 40
                    color: index % 2 === 0 ? "#f9f9f9" : "white"
                    
                    Row {
                        anchors.verticalCenter: parent.verticalCenter
                        spacing: 20
                        
                        // Toggle switch
                        Switch {
                            checked: modelData.enabled
                            anchors.verticalCenter: parent.verticalCenter
                            
                            indicator: Rectangle {
                                implicitWidth: 40
                                implicitHeight: 20
                                x: parent.leftPadding
                                y: parent.height / 2 - height / 2
                                radius: 10
                                color: parent.checked ? "#00BCD4" : "#cccccc"
                                
                                Rectangle {
                                    x: parent.parent.checked ? parent.width - width : 0
                                    width: 18
                                    height: 18
                                    radius: 9
                                    anchors.verticalCenter: parent.verticalCenter
                                    color: "white"
                                    
                                    Behavior on x {
                                        NumberAnimation { duration: 200 }
                                    }
                                }
                            }
                        }
                        
                        Text {
                            text: modelData.slot
                            font.pointSize: 12
                            width: 80
                            anchors.verticalCenter: parent.verticalCenter
                        }
                        
                        Text {
                            text: modelData.carrier
                            font.pointSize: 12
                            width: 120
                            anchors.verticalCenter: parent.verticalCenter
                        }
                        
                        // Signal strength
                        Rectangle {
                            width: 80
                            height: 20
                            color: "transparent"
                            anchors.verticalCenter: parent.verticalCenter
                            
                            Row {
                                anchors.centerIn: parent
                                spacing: 2
                                
                                Repeater {
                                    model: modelData.signal > 0 ? 4 : 0
                                    
                                    Rectangle {
                                        width: 8
                                        height: 6 + index * 3
                                        color: index < modelData.signal ? "#00BCD4" : "#cccccc"
                                        anchors.bottom: parent.bottom
                                    }
                                }
                                
                                // Special icons for PSTN and satellite
                                Text {
                                    visible: modelData.signal === -1
                                    text: modelData.carrier === "PSTN" ? "📞" : "🌐"
                                    font.pointSize: 16
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                            }
                        }
                        
                        Text {
                            text: modelData.type
                            font.pointSize: 12
                            width: 80
                            anchors.verticalCenter: parent.verticalCenter
                        }
                        
                        Row {
                            spacing: 10
                            anchors.verticalCenter: parent.verticalCenter
                            
                            Text {
                                text: "↑ " + modelData.upload
                                font.pointSize: 10
                                color: "#00BCD4"
                            }
                            
                            Text {
                                text: "↓ " + modelData.download
                                font.pointSize: 10
                                color: "#4CAF50"
                            }
                        }
                    }
                }
            }
        }
    }
}