import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12

Rectangle {
    color: "#f5f5f5"
    
    Row {
        anchors.fill: parent
        
        // Left sidebar navigation
        Rectangle {
            width: 200
            height: parent.height
            color: "#e0e0e0"
            
            Column {
                anchors.fill: parent
                
                // Header
                Rectangle {
                    width: parent.width
                    height: 60
                    color: "#c41e3a"
                    
                    Text {
                        anchors.centerIn: parent
                        text: "设置"
                        color: "white"
                        font.pointSize: 16
                        font.bold: true
                    }
                }
                
                // Navigation items
                Column {
                    width: parent.width
                    
                    Rectangle {
                        width: parent.width
                        height: 50
                        color: "#c41e3a"
                        
                        Row {
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.leftMargin: 15
                            spacing: 10
                            
                            Rectangle {
                                width: 6
                                height: 6
                                color: "white"
                                radius: 3
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            
                            Text {
                                text: "常用设置"
                                color: "white"
                                font.pointSize: 12
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                    }
                    
                    Rectangle {
                        width: parent.width
                        height: 50
                        color: "#c41e3a"
                        
                        Row {
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.leftMargin: 35
                            spacing: 10
                            
                            Rectangle {
                                width: 8
                                height: 8
                                color: "white"
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            
                            Text {
                                text: "用户编辑"
                                color: "white"
                                font.pointSize: 11
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                    }
                    
                    Rectangle {
                        width: parent.width
                        height: 50
                        color: "transparent"
                        
                        Row {
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.leftMargin: 15
                            spacing: 10
                            
                            Text {
                                text: "👤"
                                font.pointSize: 14
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            
                            Text {
                                text: "账号管理"
                                color: "#333333"
                                font.pointSize: 12
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                    }
                    
                    Rectangle {
                        width: parent.width
                        height: 50
                        color: "transparent"
                        
                        Row {
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.leftMargin: 15
                            spacing: 10
                            
                            Text {
                                text: "🌐"
                                font.pointSize: 14
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            
                            Text {
                                text: "网络设置"
                                color: "#333333"
                                font.pointSize: 12
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                    }
                    
                    Rectangle {
                        width: parent.width
                        height: 50
                        color: "transparent"
                        
                        Row {
                            anchors.left: parent.left
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.leftMargin: 15
                            spacing: 10
                            
                            Text {
                                text: "⚙"
                                font.pointSize: 14
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            
                            Text {
                                text: "系统维护"
                                color: "#333333"
                                font.pointSize: 12
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                    }
                }
            }
        }
        
        // Main content area
        Rectangle {
            width: parent.width - 200
            height: parent.height
            color: "white"
            
            Column {
                anchors.fill: parent
                anchors.margins: 30
                spacing: 30
                
                // Tab buttons
                Row {
                    spacing: 20
                    
                    Rectangle {
                        width: 100
                        height: 40
                        color: "#c41e3a"
                        radius: 5
                        
                        Text {
                            anchors.centerIn: parent
                            text: "号码设置"
                            color: "white"
                            font.pointSize: 12
                        }
                    }
                    
                    Rectangle {
                        width: 100
                        height: 40
                        color: "transparent"
                        border.color: "#c41e3a"
                        border.width: 1
                        radius: 5
                        
                        Text {
                            anchors.centerIn: parent
                            text: "时间设置"
                            color: "#c41e3a"
                            font.pointSize: 12
                        }
                    }
                    
                    Rectangle {
                        width: 100
                        height: 40
                        color: "transparent"
                        border.color: "#c41e3a"
                        border.width: 1
                        radius: 5
                        
                        Text {
                            anchors.centerIn: parent
                            text: "音量设置"
                            color: "#c41e3a"
                            font.pointSize: 12
                        }
                    }
                }
                
                // Content sections
                Column {
                    width: parent.width
                    spacing: 30
                    
                    // Number settings section
                    Column {
                        width: parent.width
                        spacing: 15
                        
                        Row {
                            width: parent.width
                            spacing: 20
                            
                            Text {
                                text: "话务台号码"
                                font.pointSize: 12
                                width: 150
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            
                            TextField {
                                width: 200
                                height: 35
                                text: "1234"
                                background: Rectangle {
                                    color: "#f0f0f0"
                                    border.color: "#cccccc"
                                    border.width: 1
                                    radius: 3
                                }
                            }
                        }
                        
                        Row {
                            width: parent.width
                            spacing: 20
                            
                            Text {
                                text: "本机号码"
                                font.pointSize: 12
                                width: 150
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            
                            TextField {
                                width: 200
                                height: 35
                                text: "8560"
                                background: Rectangle {
                                    color: "#f0f0f0"
                                    border.color: "#cccccc"
                                    border.width: 1
                                    radius: 3
                                }
                            }
                        }
                    }
                    
                    Rectangle {
                        width: parent.width
                        height: 1
                        color: "#e0e0e0"
                    }
                    
                    // Time settings section  
                    Column {
                        width: parent.width
                        spacing: 15
                        
                        Row {
                            width: parent.width
                            spacing: 20
                            
                            Text {
                                text: "北京时"
                                font.pointSize: 12
                                width: 150
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            
                            Text {
                                text: "2020-10-09 14:20:31"
                                font.pointSize: 12
                                color: "#666666"
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                        
                        Row {
                            width: parent.width
                            spacing: 20
                            
                            Text {
                                text: "当地时"
                                font.pointSize: 12
                                width: 150
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            
                            Text {
                                text: "(UTC 20:20)夏威夷"
                                font.pointSize: 12
                                color: "#666666"
                                anchors.verticalCenter: parent.verticalCenter
                            }
                        }
                        
                        Row {
                            width: parent.width
                            spacing: 20
                            
                            Text {
                                text: "24小时制"
                                font.pointSize: 12
                                width: 150
                                anchors.verticalCenter: parent.verticalCenter
                            }
                            
                            Switch {
                                checked: true
                                anchors.verticalCenter: parent.verticalCenter
                                
                                indicator: Rectangle {
                                    implicitWidth: 50
                                    implicitHeight: 25
                                    x: parent.leftPadding
                                    y: parent.height / 2 - height / 2
                                    radius: 12
                                    color: parent.checked ? "#00BCD4" : "#cccccc"
                                    
                                    Rectangle {
                                        x: parent.parent.checked ? parent.width - width : 0
                                        width: 23
                                        height: 23
                                        radius: 11
                                        anchors.verticalCenter: parent.verticalCenter
                                        color: "white"
                                        
                                        Behavior on x {
                                            NumberAnimation { duration: 200 }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    Rectangle {
                        width: parent.width
                        height: 1
                        color: "#e0e0e0"
                    }
                    
                    // Volume settings section
                    Column {
                        width: parent.width
                        spacing: 20
                        
                        Repeater {
                            model: [
                                {name: "手柄音量", value: 21},
                                {name: "扬声音量", value: 18},
                                {name: "铃声音量", value: 15},
                                {name: "发话音量", value: 15}
                            ]
                            
                            Row {
                                width: parent.width
                                spacing: 20
                                
                                Text {
                                    text: modelData.name
                                    font.pointSize: 12
                                    width: 150
                                    anchors.verticalCenter: parent.verticalCenter
                                }
                                
                                Row {
                                    spacing: 10
                                    anchors.verticalCenter: parent.verticalCenter
                                    
                                    Button {
                                        width: 30
                                        height: 30
                                        text: "−"
                                        background: Rectangle {
                                            color: "#c41e3a"
                                            radius: 15
                                        }
                                        contentItem: Text {
                                            text: parent.text
                                            color: "white"
                                            font.pointSize: 16
                                            horizontalAlignment: Text.AlignHCenter
                                            verticalAlignment: Text.AlignVCenter
                                        }
                                    }
                                    
                                    Text {
                                        text: modelData.value.toString()
                                        font.pointSize: 12
                                        width: 30
                                        horizontalAlignment: Text.AlignHCenter
                                        anchors.verticalCenter: parent.verticalCenter
                                    }
                                    
                                    Button {
                                        width: 30
                                        height: 30
                                        text: "+"
                                        background: Rectangle {
                                            color: "#c41e3a"
                                            radius: 15
                                        }
                                        contentItem: Text {
                                            text: parent.text
                                            color: "white"
                                            font.pointSize: 16
                                            horizontalAlignment: Text.AlignHCenter
                                            verticalAlignment: Text.AlignVCenter
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}