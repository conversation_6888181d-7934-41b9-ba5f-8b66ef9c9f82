#include "Mp4Checker.h"
#include <iostream>
#include <fstream>
#include <iomanip>
bool Mp4Checker::check(const std::string& filePath) {
        uint64_t total = 0;
        
        try {
            // 打开文件
            std::ifstream file(filePath, std::ios::binary);
            if (!file.is_open()) {
                std::cerr << "无法打开文件: " << filePath << std::endl;
                return false;
            }
            
            // 获取文件实际大小
            file.seekg(0, std::ios::end);
            uint64_t realSize = file.tellg();
            file.seekg(0, std::ios::beg);
            
            bool readLarge = false;
            uint64_t size;
            uint8_t buff[8];
            
            while (file.read(reinterpret_cast<char*>(buff), sizeof(buff))) {
                if (readLarge) {
                    // 读取64位大小（大端序）
                    size = readBigEndianUInt64(buff);
                } else {
                    // 读取32位大小（大端序）
                    size = readBigEndianUInt32(buff);
                }
                
                if (size == 0) {
                    // size为0表示这是最后一个box，占用文件剩余所有空间
                    break;
                }
                
                if (size == 1) {
                    // size为1表示使用64位largeSize字段
                    readLarge = true;
                } else {
                    total += size;
                    uint64_t skip;
                    
                    if (readLarge) {
                        // 跳过 size(8字节) + type(4字节) + largeSize(8字节) = 16字节
                        skip = size - 16;
                    } else {
                        // 跳过 size(4字节) + type(4字节) = 8字节
                        skip = size - 8;
                    }
                    
                    if (skip > 0) {
                        file.seekg(skip, std::ios::cur);
                        if (file.fail()) {
                            std::cerr << "文件读取错误，可能文件已损坏" << std::endl;
                            return false;
                        }
                    }
                    
                    readLarge = false;
                }
            }
            
            file.close();
            
            // 比较计算出的总大小与实际文件大小
            return (realSize == total);
            
        } catch (const std::exception& e) {
            std::cerr << "检查文件时发生异常: " << e.what() << std::endl;
            return false;
        }
    }

bool Mp4Checker::checkVerbose(const std::string& filePath, bool verbose) {
    uint64_t total = 0;

    try {
        std::ifstream file(filePath, std::ios::binary);
        if (!file.is_open()) {
            if (verbose) std::cerr << "无法打开文件: " << filePath << std::endl;
            return false;
        }

        file.seekg(0, std::ios::end);
        uint64_t realSize = file.tellg();
        file.seekg(0, std::ios::beg);

        if (verbose) {
            std::cout << "文件大小: " << realSize << " 字节" << std::endl;
            std::cout << "开始解析MP4 box结构..." << std::endl;
        }

        bool readLarge = false;
        uint64_t size;
        uint8_t buff[8];
        int boxCount = 0;

        while (file.read(reinterpret_cast<char*>(buff), sizeof(buff))) {
            boxCount++;

            if (readLarge) {
                size = readBigEndianUInt64(buff);
            } else {
                size = readBigEndianUInt32(buff);
                // 读取box类型
                if (verbose) {
                    std::string boxType = getBoxType(buff + 4);
                    std::cout << "Box #" << boxCount << ": " << boxType
                              << ", size: " << size << std::endl;
                }
            }

            if (size == 0) {
                if (verbose) std::cout << "遇到size=0的box，表示占用剩余空间" << std::endl;
                break;
            }

            if (size == 1) {
                if (verbose) std::cout << "遇到size=1的box，使用64位largeSize" << std::endl;
                readLarge = true;
            } else {
                total += size;
                uint64_t skip = readLarge ? (size - 16) : (size - 8);

                if (skip > 0) {
                    file.seekg(skip, std::ios::cur);
                    if (file.fail()) {
                        if (verbose) std::cerr << "文件读取错误，可能文件已损坏" << std::endl;
                        return false;
                    }
                }
                readLarge = false;
            }
        }

        file.close();

        if (verbose) {
            std::cout << "解析完成，共处理 " << boxCount << " 个box" << std::endl;
            std::cout << "计算总大小: " << total << " 字节" << std::endl;
            std::cout << "实际文件大小: " << realSize << " 字节" << std::endl;
        }

        return (realSize == total);

    } catch (const std::exception& e) {
        if (verbose) std::cerr << "检查文件时发生异常: " << e.what() << std::endl;
        return false;
    }
}

uint32_t Mp4Checker::readBigEndianUInt32(const uint8_t* buffer) {
        return (static_cast<uint32_t>(buffer[0]) << 24) |
               (static_cast<uint32_t>(buffer[1]) << 16) |
               (static_cast<uint32_t>(buffer[2]) << 8) |
               static_cast<uint32_t>(buffer[3]);
    }
    
uint64_t Mp4Checker::readBigEndianUInt64(const uint8_t* buffer) {
        return (static_cast<uint64_t>(buffer[0]) << 56) |
               (static_cast<uint64_t>(buffer[1]) << 48) |
               (static_cast<uint64_t>(buffer[2]) << 40) |
               (static_cast<uint64_t>(buffer[3]) << 32) |
               (static_cast<uint64_t>(buffer[4]) << 24) |
               (static_cast<uint64_t>(buffer[5]) << 16) |
               (static_cast<uint64_t>(buffer[6]) << 8) |
               static_cast<uint64_t>(buffer[7]);
}

std::string Mp4Checker::getBoxType(const uint8_t* buffer) {
    std::string type;
    for (int i = 0; i < 4; i++) {
        if (buffer[i] >= 32 && buffer[i] <= 126) {  // 可打印ASCII字符
            type += static_cast<char>(buffer[i]);
        } else {
            type += '?';
        }
    }
    return type;
}

// 测试函数
int main(int argc, char* argv[]) {
    std::string filePath;
    bool verbose = false;

    if (argc < 2) {
        std::cout << "用法: " << argv[0] << " <MP4文件路径> [-v]" << std::endl;
        std::cout << "  -v: 显示详细信息" << std::endl;
        return 1;
    }

    filePath = argv[1];
    if (argc > 2 && std::string(argv[2]) == "-v") {
        verbose = true;
    }

    bool isValid;
    if (verbose) {
        isValid = Mp4Checker::checkVerbose(filePath, true);
    } else {
        isValid = Mp4Checker::check(filePath);
    }

    if (isValid) {
        std::cout << "✓ MP4文件完整" << std::endl;
    } else {
        std::cout << "✗ MP4文件损坏或不完整" << std::endl;
    }

    return isValid ? 0 : 1;
}
