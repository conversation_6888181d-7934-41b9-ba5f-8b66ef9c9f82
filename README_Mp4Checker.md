# MP4 文件完整性检查器

这是一个从 Java 版本转换而来的 C++ MP4 文件完整性检查工具。

## 功能

- 检查 MP4 文件是否完整
- 通过解析 MP4 的 box 结构来验证文件大小
- 支持标准 32 位和扩展 64 位 box 大小
- 提供详细的调试输出模式

## 编译

```bash
# 使用提供的 Makefile
make -f Mp4Checker.mk

# 或者直接编译
g++ -std=c++11 -Wall -Wextra -O2 -o mp4checker Mp4Checker.cpp
```

## 使用方法

### 基本检查
```bash
./mp4checker video.mp4
```

### 详细模式（显示调试信息）
```bash
./mp4checker video.mp4 -v
```

## 输出示例

### 基本模式
```
✓ MP4文件完整
```
或
```
✗ MP4文件损坏或不完整
```

### 详细模式
```
文件大小: 1048576 字节
开始解析MP4 box结构...
Box #1: ftyp, size: 32
Box #2: moov, size: 2048
Box #3: mdat, size: 1046496
解析完成，共处理 3 个box
计算总大小: 1048576 字节
实际文件大小: 1048576 字节
✓ MP4文件完整
```

## 原理

MP4 文件由一系列的 "box"（也称为 "atom"）组成。每个 box 都有以下结构：

```
[4字节大小][4字节类型][数据...]
```

对于大文件，可能使用扩展格式：
```
[4字节=1][4字节类型][8字节大小][数据...]
```

本工具通过遍历所有 box，累加它们的大小，然后与文件的实际大小进行比较来判断文件是否完整。

## 与 Java 版本的主要差异

1. **内存管理**: C++ 版本使用 RAII 和智能指针管理资源
2. **错误处理**: 使用异常和返回值进行错误处理
3. **字节序处理**: 手动实现大端序字节读取
4. **文件操作**: 使用 C++ 标准库的 fstream
5. **调试功能**: 增加了详细的调试输出模式

## API 使用

如果要在其他 C++ 项目中使用：

```cpp
#include "Mp4Checker.h"

// 简单检查
bool isValid = Mp4Checker::check("video.mp4");

// 详细检查
bool isValid = Mp4Checker::checkVerbose("video.mp4", true);
```

## 注意事项

- 仅支持标准的 MP4 文件格式
- 对于损坏的文件头，可能无法正确检测
- 大文件检查可能需要一些时间
- 确保有足够的权限读取目标文件
