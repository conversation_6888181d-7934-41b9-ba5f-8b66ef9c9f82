#if 0
static void qLogHandler(QtMsgType type, const QMessageLogContext & ctx, const QString & msg) {
    if (type < LOG_LEVEL)
        return;

    //enum QtMsgType { QtDebugMsg, QtWarningMsg, QtCriticalMsg, QtFatalMsg, QtInfoMsg, QtSystemMsg = QtCriticalMsg };
    static char s_types[5][6] = {"DEBUG", "WARN ", "ERROR", "FATAL", "INFO "};
    const char* szType = "DEBUG";
    if (type < 5) {
        szType = s_types[(int)type];
    }

#ifdef QT_NO_DEBUG
    QString strLog = QString::asprintf("[%s][%s] %s\n",
                                       QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz").toLocal8Bit().data(),
                                       szType,
                                       msg.toLocal8Bit().data());
#else
    QString strLog = QString::asprintf("[%s][%s] %s [%s:%d-%s]\n",
                                       QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz").toLocal8Bit().data(),
                                       szType,
                                       msg.toLocal8Bit().data(),
                                       ctx.file,ctx.line,ctx.function);
#endif

    static FILE* s_fp = NULL;
    if (s_fp) {
        fseek(s_fp, 0, SEEK_END);
        if (ftell(s_fp) > (2 << 20)) {
            fclose(s_fp);
            s_fp = NULL;
        }
    }

    if (!s_fp) {
        char logfile[256];
        snprintf(logfile, sizeof(logfile), "%s/logs/qt.log", g_exec_dir);
        s_fp = fopen(logfile, "w");
    }

    if (s_fp) {
        fputs(strLog.toLocal8Bit().data(), s_fp);
    }
}

static int load_confile() {
    get_executable_path(g_exec_path, sizeof(g_exec_path));
    get_executable_dir(g_exec_dir, sizeof(g_exec_dir));
    get_run_dir(g_run_dir, sizeof(g_run_dir));

    // load confile
    g_confile = new IniParser;
    snprintf(g_conf_file, sizeof(g_conf_file), "%s/conf/%s.conf", g_exec_dir, APP_NAME);
    if (access(g_conf_file, 0) != 0) {
        QFile::copy(QString(g_exec_dir) + "/conf/" APP_NAME ".conf.default", g_conf_file);
    }
    g_confile->LoadFromFile(g_conf_file);
    // logfile
    string str = g_confile->GetValue("logfile");
    if (str.empty()) {
        snprintf(g_log_file, sizeof(g_log_file), "%s/logs", g_exec_dir);
        hv_mkdir_p(g_log_file);
        snprintf(g_log_file, sizeof(g_log_file), "%s/logs/%s.log", g_exec_dir, APP_NAME);
    }
    else {
        strncpy(g_log_file, str.c_str(), sizeof(g_log_file));
    }
    hlog_set_file(g_log_file);
    // loglevel
    str = g_confile->GetValue("loglevel");
    if (!str.empty()) {
        hlog_set_level_by_str(str.c_str());
    }
    // log_filesize
    str = g_confile->GetValue("log_filesize");
    if (!str.empty()) {
        hlog_set_max_filesize_by_str(str.c_str());
    }
    // log_remain_days
    str = g_confile->GetValue("log_remain_days");
    if (!str.empty()) {
        hlog_set_remain_days(atoi(str.c_str()));
    }
    // log_fsync
    str = g_confile->GetValue("log_fsync");
    if (!str.empty()) {
        logger_enable_fsync(hlog, getboolean(str.c_str()));
    }
    // first log here
    hlogi("%s version: %s", g_exec_path, hv_compile_version());
    hlog_fsync();
    return 0;
}

int main(int argc, char *argv[]) {
    load_confile();

    qInstallMessageHandler(qLogHandler);
    qInfo("-------------------app start----------------------------------");
    QApplication app(argc, argv);
    app.setApplicationName(APP_NAME);

    string str = g_confile->GetValue("skin", "ui");
    loadSkin(str.empty() ? DEFAULT_SKIN : str.c_str());

    str = g_confile->GetValue("palette", "ui");
    setPalette(str.empty() ? DEFAULT_PALETTE_COLOR : strtoul(str.c_str(), NULL, 16));

    str = g_confile->GetValue("language", "ui");
    loadLang(str.empty() ? DEFAULT_LANGUAGE : str.c_str());

    setFont(g_confile->Get<int>("fontsize", "ui", DEFAULT_FONT_SIZE));

    // NOTE: replace with image.qrc
    // rcloader->loadIcon();

    MainWindow::instance();
    g_mainwnd->window_state = (MainWindow::window_state_e)(g_confile->Get<int>("main_window_state", "ui"));
    switch (g_mainwnd->window_state) {
    case MainWindow::FULLSCREEN:
        g_mainwnd->showFullScreen();
        break;
    case MainWindow::MAXIMIZED:
        g_mainwnd->showMaximized();
        break;
    case MainWindow::MINIMIZED:
        g_mainwnd->showMinimized();
        break;
    default:
        str = g_confile->GetValue("main_window_rect", "ui");
        if (!str.empty()) {
            int x,y,w,h;
            x = y = w = h = 0;
            sscanf(str.c_str(), "rect(%d,%d,%d,%d)", &x, &y, &w, &h);
            if (w && h) {
                g_mainwnd->setGeometry(x, y, w, h);
            }
        }
        g_mainwnd->show();
        break;
    }
    if (g_confile->Get<bool>("mv_fullscreen", "ui")) {
        g_mainwnd->mv_fullscreen();
    }

    int exitcode = app.exec();

    g_confile->Set<int>("main_window_state", (int)g_mainwnd->window_state, "ui");
    str = asprintf("rect(%d,%d,%d,%d)",
                    g_mainwnd->x(),
                    g_mainwnd->y(),
                    g_mainwnd->width(),
                    g_mainwnd->height());
    g_confile->SetValue("main_window_rect", str, "ui");

    MainWindow::exitInstance();
    qInfo("-------------------app end----------------------------------");

    g_confile->Save();
    SAFE_DELETE(g_confile);

    return exitcode;
}
#endif
#include "appdef.h"
#include "confile.h"
#include <iostream>
#include <QDebug>
IniParser* g_confile = NULL;
extern "C" {
#include "libavutil/avutil.h"
#include "libavutil/pixdesc.h"
#include "libavcodec/avcodec.h"
#include "libavformat/avformat.h"
#include "libavdevice/avdevice.h"
#include "libswscale/swscale.h"
#include <libavutil/timestamp.h>
#include <libavutil/imgutils.h>
}
#include <opencv2/opencv.hpp>
static cv::Mat avFrameToCvMat(AVFrame* frame, SwsContext* sws_ctx) {
    cv::Mat bgr(frame->height, frame->width, CV_8UC3);

    int stride = 3 * frame->width;
    sws_scale(sws_ctx, (const uint8_t* const*)frame->data, frame->linesize, 0, frame->height, &bgr.data,
        &stride);

    //sws_freeContext(sws_ctx);
    return bgr;
}
using namespace cv;
int  iCheckMosaicImage(const Mat& srcFrame)
{
    Mat srcFrameGray;

    int l_iTotalCorner = 0;

    cvtColor(srcFrame, srcFrameGray, COLOR_BGR2GRAY);

    cornerHarris(srcFrameGray, srcFrameGray, 2, 3, 0.04);
    threshold(srcFrameGray, srcFrameGray, 0.010, 255, cv::THRESH_BINARY);

    //imshow("srcFrameGray", srcFrameGray);

    int l_iRow = srcFrameGray.rows;
    int l_iCol = srcFrameGray.cols;


    for (int i = 0; i < l_iRow; i++)
    {
        for (int j = 0; j < l_iCol; j++)
        {
            if (srcFrameGray.at<float>(i, j) == 255)//值255为角点
            {
                l_iTotalCorner++;
            }
        }
    }

    return l_iTotalCorner;

}

void test(const char* filename);
int main() {
	test("C://Users//lu//Downloads//video1.mp4");
    return 0;
}
void test(const char* filename) {
    int ret = 0;
    int err;
    int output_pts = 0; //递增pts，用于丢帧后可以无缝衔接上后面的帧
    //打开输入文件
    //动态分配和初始化一个空的AVFormatContext对象，为后续的音视频封装和解封装操作做准备。
    AVFormatContext* fmt_ctx = avformat_alloc_context();

    if (!fmt_ctx)
    {
        qDebug() << "fmt_ctx error code:" << AVERROR(ENOMEM);
        return;
    }

    //打开文件
    if ((err = avformat_open_input(&fmt_ctx, filename, NULL, NULL)) < 0)
    {
        printf("can not open file %d \n", err);
        return;
    }


    //分配并初始化一个 AVCodecContext 结构体，该结构体用于编解码器的相关信息和参数设置。
    AVCodecContext* avctx = avcodec_alloc_context3(NULL);

    //用于将AVCodecParameters结构体的值赋值给AVCodecContext结构体的对应字段
    //参数值的赋值涉及到了编解码器的基本配置，例如编码类型、码率、帧率等等。这样可以方便地将参数信息传递给编解码器上下文，以供后续的编解码操作使用。
    ret = avcodec_parameters_to_context(avctx, fmt_ctx->streams[0]->codecpar);

    if (ret < 0)
    {
        qDebug() << "avcodec_parameters_to_context error code:" << ret;
        return;
    }

    //查找解码器
    AVCodec* codec = avcodec_find_decoder(avctx->codec_id);

    //打开解码器
    if ((ret = avcodec_open2(avctx, codec, NULL)) < 0)
    {
        qDebug() << "avcodec_open2 error code:" << ret;
        return;
    }

    //打开输出文件容器
    char filename_out[] = "juren-30s-5.mp4";
    AVFormatContext* fmt_ctx_out = NULL;
    //该函数会尝试通过指定的输出格式来分配一个输出格式上下文
    err = avformat_alloc_output_context2(&fmt_ctx_out, NULL, NULL, filename_out);

    if (!fmt_ctx_out)
    {
        qDebug() << "error code:" << AVERROR(ENOMEM);
        return;
    }

    //添加一路流到容器上下文
    AVStream* st = avformat_new_stream(fmt_ctx_out, NULL);
    st->time_base = fmt_ctx->streams[0]->time_base;

    //分配帧和包资源
    AVCodecContext* enc_ctx = NULL;
    AVPacket* pkt = av_packet_alloc();
    AVFrame* frame = av_frame_alloc();
    AVPacket* pkt_out = av_packet_alloc();

    int frame_num = 0;
    int read_end = 0;
	SwsContext* sws_ctx = NULL;
    
    int skip_frame_count = 0;
    int now_frame_count = 0;

    //不知道是特定格式还是其他原因,此时avctx->framerate还是未知,只能留到后面计算了
    //每次pts需要增加的增量,计算为流的time_base的分母 除以 帧率
    //int frameIncPts = st->time_base.den / avctx->framerate.num;
    int frameIncPts = -1;
    while (true)
    {
        if (1 == read_end)
        {
            break;
        }

        ret = av_read_frame(fmt_ctx, pkt);

        //跳过不处理音频包
        if (1 == pkt->stream_index)
        {
            av_packet_unref(pkt);
            continue;
        }

        //读取到文件的结尾了
        if (AVERROR_EOF == ret)
        {
            //读取完文件，这时候 pkt 的 data 跟 size 应该是 null
            avcodec_send_packet(avctx, NULL);
        }
        else
        {
            if (0 != ret)
            {
                qDebug() << "av_read_frame error code:" << ret;
                return;
            }
            else
            {
            retry:
                if (avcodec_send_packet(avctx, pkt) == /*AVERROR(EAGAIN)*/ -11)
                {
                    qDebug() << "Receive_frame and send_packet both returned EAGAIN, which is an API violation";
                    //这里可以考虑休眠 0.1 秒，返回 EAGAIN 通常是 ffmpeg 的内部 api 有bug
                    goto retry;
                }

                //释放 pkt 里面的编码数据
                av_packet_unref(pkt);
            }
        }

        //循环不断从解码器读数据，直到没有数据可读。

        while (true)
        {
            //读取 AVFrame
            ret = avcodec_receive_frame(avctx, frame);
            /* 释放 frame 里面的YUV数据，
             * 由于 avcodec_receive_frame 函数里面会调用 av_frame_unref，所以下面的代码可以注释。
             * 所以我们不需要 手动 unref 这个 AVFrame
             * */
             //av_frame_unref(frame);

            if (/*AVERROR(EAGAIN)*/ -11 == ret)
            {
                //提示 EAGAIN 代表 解码器 需要 更多的 AVPacket
                //跳出 第一层 for，让 解码器拿到更多的 AVPacket
                break;
            }
            else if (AVERROR_EOF == ret)
            {
                /* 提示 AVERROR_EOF 代表之前已经往 解码器发送了一个 data 跟 size 都是 NULL 的 AVPacket
                 * 发送 NULL 的 AVPacket 是提示解码器把所有的缓存帧全都刷出来。
                 * 通常只有在 读完输入文件才会发送 NULL 的 AVPacket，或者需要用现有的解码器解码另一个的视频流才会这么干。
                 *
                 * */

                 /* 往编码器发送 null 的 AVFrame，让编码器把剩下的数据刷出来。*/
                ret = avcodec_send_frame(enc_ctx, NULL);
                for (;;)
                {
                    ret = avcodec_receive_packet(enc_ctx, pkt_out);
                    //这里不可能返回 EAGAIN，如果有直接退出。
                    if (ret == /*AVERROR(EAGAIN)*/-11) {
                        printf("avcodec_receive_packet error code %d \n", ret);
                        return;
                    }
                    if (AVERROR_EOF == ret) {
                        break;
                    }
                    //编码出 AVPacket ，先打印一些信息，然后把它写入文件。
                    printf("pkt_out size : %d \n", pkt_out->size);
                    //设置 AVPacket 的 stream_index ，这样才知道是哪个流的。
                    pkt_out->stream_index = st->index;
                    //转换 AVPacket 的时间基为 输出流的时间基。
                    pkt_out->pts = av_rescale_q_rnd(pkt_out->pts, fmt_ctx->streams[0]->time_base, st->time_base, static_cast<AVRounding>(AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX));
                    pkt_out->dts = av_rescale_q_rnd(pkt_out->dts, fmt_ctx->streams[0]->time_base, st->time_base, static_cast<AVRounding>(AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX));
                    pkt_out->duration = av_rescale_q_rnd(pkt_out->duration, fmt_ctx->streams[0]->time_base, st->time_base, static_cast<AVRounding>(AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX));


                    ret = av_interleaved_write_frame(fmt_ctx_out, pkt_out);
                    if (ret < 0) {
                        printf("av_interleaved_write_frame faile %d \n", ret);
                        return;
                    }
                    av_packet_unref(pkt_out);
                }
                av_write_trailer(fmt_ctx_out);
                //跳出 第二层 for，文件已经解码完毕。
                read_end = 1;
                break;
            }
            else if (ret >= 0)
            {
                ++now_frame_count;
                //只有解码出来一个帧，才可以开始初始化编码器。
                sws_ctx = sws_getCachedContext(sws_ctx, frame->width, frame->height, (AVPixelFormat)frame->format,
                    frame->width, frame->height, AV_PIX_FMT_BGR24,
                    SWS_BICUBIC, nullptr, nullptr, nullptr);
                cv::Mat bgr = avFrameToCvMat(frame, sws_ctx);
                if (iCheckMosaicImage(bgr) > 10) {
                    ++skip_frame_count;
                    continue; // 过滤角点大于10的帧
                }
                if (NULL == enc_ctx)
                {
                    //打开编码器，并且设置 编码信息。
                    //AVCodec* encode = avcodec_find_encoder(AV_CODEC_ID_H264);
                    AVCodec* encode = avcodec_find_encoder(avctx->codec_id);
                    enc_ctx = avcodec_alloc_context3(encode);
                    enc_ctx->codec_type = AVMEDIA_TYPE_VIDEO;
                    enc_ctx->bit_rate = avctx->bit_rate;
                    enc_ctx->framerate = avctx->framerate;
                    //enc_ctx->gop_size = 30;
                    //enc_ctx->max_b_frames = 0;
                    enc_ctx->profile = FF_PROFILE_H264_BASELINE;
                    /*
                     * 其实下面这些信息在容器那里也有，也可以一开始直接在容器那里打开编码器
                     * 我从 AVFrame 里拿这些编码器参数是因为，容器的信息不一样就是最终的信息。
                     * 因为你解码出来的 AVFrame 可能会经过 filter 滤镜，经过滤镜之后信息就会变化，但是本文没有使用滤镜。
                     */
                     //编码器的时间基要取 AVFrame 的时间基，因为 AVFrame 是输入。
                    enc_ctx->time_base = fmt_ctx->streams[0]->time_base;
                    enc_ctx->width = fmt_ctx->streams[0]->codecpar->width;
                    enc_ctx->height = fmt_ctx->streams[0]->codecpar->height;
                    enc_ctx->sample_aspect_ratio = st->sample_aspect_ratio = frame->sample_aspect_ratio;
                    enc_ctx->pix_fmt = static_cast<AVPixelFormat>(frame->format);
                    enc_ctx->color_range = frame->color_range;
                    enc_ctx->color_primaries = frame->color_primaries;
                    enc_ctx->color_trc = frame->color_trc;
                    enc_ctx->colorspace = frame->colorspace;
                    enc_ctx->chroma_sample_location = frame->chroma_location;

                    /* 注意，这个 field_order 不同的视频的值是不一样的，这里我写死了。
                     * 因为 本文的视频就是 AV_FIELD_PROGRESSIVE
                     * 生产环境要对不同的视频做处理的
                     */
                    //enc_ctx->field_order = AV_FIELD_PROGRESSIVE;
					enc_ctx->field_order = avctx->field_order;

                    /* 现在我们需要把 编码器参数复制给流，解码的时候是 从流赋值参数给解码器。
                     * 现在要反着来。
                     * */
                    ret = avcodec_parameters_from_context(st->codecpar, enc_ctx);
                    if (ret < 0)
                    {
                        qDebug() << "avcodec_parameters_from_context codec faile:" << ret;
                        return;
                    }

                    //它用于初始化和打开音频或视频编解码器的上下文。
                    if ((ret = avcodec_open2(enc_ctx, encode, NULL)) < 0) {
                        qDebug() << "avcodec_open2 codec faile:" << ret;
                        return;
                    }

                    //正式打开输出文件
                    if ((ret = avio_open2(&fmt_ctx_out->pb, filename_out, AVIO_FLAG_WRITE, &fmt_ctx_out->interrupt_callback, NULL)) < 0)
                    {
                        qDebug() << "avio_open2 codec faile:" << ret;
                        return;
                    }

                    //要先写入文件头部。
                    AVDictionary* options = nullptr;
                    av_dict_set(&options, "movflags", "faststart", 0);  // 将moov atom移到文件开头,修复opencv的VideoCapture开启视频时"moov atom not found"问题
                    ret = avformat_write_header(fmt_ctx_out, &options);
                    //av_write_trailer
                    if (ret < 0)
                    {
                        qDebug() << "avformat_write_header codec faile:" << ret;
                        return;
                    }
                }
                if (frameIncPts == -1 && avctx->framerate.num != 1)
                    frameIncPts = st->time_base.den / avctx->framerate.num;
                frame->pts = output_pts;
                //frame->pkt_dts = output_pts;
                output_pts += frameIncPts;
                //往编码器发送 AVFrame，然后不断读取 AVPacket
                ret = avcodec_send_frame(enc_ctx, frame);
                if (ret < 0)
                {
                    qDebug() << "avcodec_send_frame fail:" << ret;
                    return;
                }
                for (;;)
                {
                    ret = avcodec_receive_packet(enc_ctx, pkt_out);
                    if (ret == -11/*AVERROR(EAGAIN) */ )
                    {
                        //output_pts++;
                        break;
                    }
                    if (ret < 0)
                    {
                        qDebug() << "avcodec_receive_packet fail:" << ret;
                        return;
                    }
                    //编码出 AVPacket ，先打印一些信息，然后把它写入文件。
                    qDebug() << "pkt_out size:" << pkt_out->size;
                    
                    // 重新计算包的时间戳
                    //av_packet_rescale_ts(pkt_out, enc_ctx->time_base, st->time_base);
                    // 确保DTS <= PTS
                    
                    //设置 AVPacket 的 stream_index ，这样才知道是哪个流的。
                    pkt_out->stream_index = st->index;
                    //转换 AVPacket 的时间基为 输出流的时间基。
                    pkt_out->pts = av_rescale_q_rnd(pkt_out->pts, fmt_ctx->streams[0]->time_base, st->time_base, static_cast<AVRounding>(AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX));
                    pkt_out->dts = av_rescale_q_rnd(pkt_out->dts, fmt_ctx->streams[0]->time_base, st->time_base, static_cast<AVRounding>(AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX));
                    pkt_out->duration = av_rescale_q_rnd(pkt_out->duration, fmt_ctx->streams[0]->time_base, st->time_base, static_cast<AVRounding>(AV_ROUND_NEAR_INF | AV_ROUND_PASS_MINMAX));
                    if (pkt_out->dts > pkt_out->pts) {
                        pkt_out->dts = pkt_out->pts;
                    }
                    int64_t frame_duration = av_rescale_q(1, av_inv_q(enc_ctx->framerate), st->time_base);
                    //output_pts += frame_duration;
                    //output_pts++;
                    ret = av_interleaved_write_frame(fmt_ctx_out, pkt_out);
                    if (ret < 0) {
                        qDebug() << "av_interleaved_write_frame fail:" << ret;
                        return;
                    }
                    av_packet_unref(pkt_out);
                }
            }
            else
            {
                qDebug() << "other fail \n";
                return;
            }
        }
    }
	qDebug() << "skip_frame_count: " << skip_frame_count;
	if (sws_ctx) sws_freeContext(sws_ctx);
    av_frame_free(&frame);
    av_packet_free(&pkt);
    av_packet_free(&pkt_out);

    //关闭编码器，解码器。
    avcodec_close(avctx);
    avcodec_close(enc_ctx);

    //释放容器内存。
    avformat_free_context(fmt_ctx);
    
    //必须调 avio_closep ，要不可能会没把数据写进去，会是 0kb
    avio_closep(&fmt_ctx_out->pb);
    avformat_free_context(fmt_ctx_out);
}