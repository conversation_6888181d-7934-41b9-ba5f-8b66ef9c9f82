import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.12

Rectangle {
    color: "white"
    
    GridLayout {
        anchors.fill: parent
        anchors.margins: 20
        columns: 2
        rowSpacing: 20
        columnSpacing: 20
        
        // Main screen (top left)
        Rectangle {
            Layout.fillWidth: true
        Layout.fillHeight: true
            color: "#333333"
            border.color: "#666666"
            border.width: 2
            
            Column {
                anchors.top: parent.top
                anchors.left: parent.left
                anchors.margins: 10
                
                Text {
                    text: "本地"
                    color: "white"
                    font.pointSize: 12
                }
                
                Text {
                    text: "暂无视频源 ▼"
                    color: "#4CAF50"
                    font.pointSize: 10
                }
            }
            
            Rectangle {
                anchors.centerIn: parent
                width: 80
                height: 60
                color: "#666666"
                radius: 5
                
                Text {
                    anchors.centerIn: parent
                    text: "📹"
                    color: "#999999"
                    font.pointSize: 24
                }
            }
        }
        
        // Right screen
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "#333333"
            border.color: "#666666"
            border.width: 2
            
            Column {
                anchors.top: parent.top
                anchors.left: parent.left
                anchors.margins: 10
                
                Text {
                    text: "中屏"
                    color: "white"
                    font.pointSize: 12
                }
                
                Text {
                    text: "暂无视频源 ▼"
                    color: "#4CAF50"
                    font.pointSize: 10
                }
            }
            
            ComboBox {
                anchors.top: parent.top
                anchors.right: parent.right
                anchors.margins: 10
                width: 120
                height: 30
                model: ["视频源01", "视频源02", "视频源03"]
                background: Rectangle {
                    color: "white"
                    border.color: "#cccccc"
                    border.width: 1
                    radius: 3
                }
            }
            
            Rectangle {
                anchors.centerIn: parent
                width: 80
                height: 60
                color: "#666666"
                radius: 5
                
                Text {
                    anchors.centerIn: parent
                    text: "📹"
                    color: "#999999"
                    font.pointSize: 24
                }
            }
        }
        
        // Bottom left screen
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "#333333"
            border.color: "#666666"
            border.width: 2
            
            Column {
                anchors.top: parent.top
                anchors.left: parent.left
                anchors.margins: 10
                
                Text {
                    text: "左屏"
                    color: "white"
                    font.pointSize: 12
                }
                
                Text {
                    text: "暂无视频源 ▼"
                    color: "#4CAF50"
                    font.pointSize: 10
                }
            }
            
            Rectangle {
                anchors.centerIn: parent
                width: 80
                height: 60
                color: "#666666"
                radius: 5
                
                Text {
                    anchors.centerIn: parent
                    text: "📹"
                    color: "#999999"
                    font.pointSize: 24
                }
            }
        }
        
        // Bottom right screen
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "#333333"
            border.color: "#666666"
            border.width: 2
            
            Column {
                anchors.top: parent.top
                anchors.left: parent.left
                anchors.margins: 10
                
                Text {
                    text: "右屏"
                    color: "white"
                    font.pointSize: 12
                }
                
                Text {
                    text: "暂无视频源 ▼"
                    color: "#4CAF50"
                    font.pointSize: 10
                }
            }
            
            Rectangle {
                anchors.centerIn: parent
                width: 80
                height: 60
                color: "#666666"
                radius: 5
                
                Text {
                    anchors.centerIn: parent
                    text: "📹"
                    color: "#999999"
                    font.pointSize: 24
                }
            }
        }
    }
}