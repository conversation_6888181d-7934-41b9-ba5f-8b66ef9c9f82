[{"directory": "/root/PhoneBox/build", "command": "/usr/bin/g++  -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui  -g   -fPIC -std=gnu++11 -o CMakeFiles/PhoneBox.dir/PhoneBox_autogen/mocs_compilation.cpp.o -c /root/PhoneBox/build/PhoneBox_autogen/mocs_compilation.cpp", "file": "/root/PhoneBox/build/PhoneBox_autogen/mocs_compilation.cpp"}, {"directory": "/root/PhoneBox/build", "command": "/usr/bin/g++  -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui  -g   -fPIC -std=gnu++11 -o CMakeFiles/PhoneBox.dir/landevicemodel.cpp.o -c /root/PhoneBox/landevicemodel.cpp", "file": "/root/PhoneBox/landevicemodel.cpp"}, {"directory": "/root/PhoneBox/build", "command": "/usr/bin/g++  -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui  -g   -fPIC -std=gnu++11 -o CMakeFiles/PhoneBox.dir/main.cpp.o -c /root/PhoneBox/main.cpp", "file": "/root/PhoneBox/main.cpp"}, {"directory": "/root/PhoneBox/build", "command": "/usr/bin/g++  -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui  -g   -fPIC -std=gnu++11 -o CMakeFiles/PhoneBox.dir/optimized_video_modifier.cpp.o -c /root/PhoneBox/optimized_video_modifier.cpp", "file": "/root/PhoneBox/optimized_video_modifier.cpp"}, {"directory": "/root/PhoneBox/build", "command": "/usr/bin/g++  -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui  -g   -fPIC -std=gnu++11 -o CMakeFiles/PhoneBox.dir/videoDetect.cpp.o -c /root/PhoneBox/videoDetect.cpp", "file": "/root/PhoneBox/videoDetect.cpp"}, {"directory": "/root/PhoneBox/build", "command": "/usr/bin/g++  -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui  -g   -fPIC -std=gnu++11 -o CMakeFiles/PhoneBox.dir/video_generator.cpp.o -c /root/PhoneBox/video_generator.cpp", "file": "/root/PhoneBox/video_generator.cpp"}, {"directory": "/root/PhoneBox/build", "command": "/usr/bin/g++  -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui  -g   -fPIC -std=gnu++11 -o CMakeFiles/PhoneBox.dir/video_modifier.cpp.o -c /root/PhoneBox/video_modifier.cpp", "file": "/root/PhoneBox/video_modifier.cpp"}, {"directory": "/root/PhoneBox/build", "command": "/usr/bin/g++  -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui  -g   -fPIC -std=gnu++11 -o CMakeFiles/PhoneBox.dir/video_normalizer.cpp.o -c /root/PhoneBox/video_normalizer.cpp", "file": "/root/PhoneBox/video_normalizer.cpp"}, {"directory": "/root/PhoneBox/build", "command": "/usr/bin/g++  -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui  -g   -fPIC -std=gnu++11 -o CMakeFiles/PhoneBox.dir/PhoneBox_autogen/EWIEGA46WW/qrc_qml.cpp.o -c /root/PhoneBox/build/PhoneBox_autogen/EWIEGA46WW/qrc_qml.cpp", "file": "/root/PhoneBox/build/PhoneBox_autogen/EWIEGA46WW/qrc_qml.cpp"}]