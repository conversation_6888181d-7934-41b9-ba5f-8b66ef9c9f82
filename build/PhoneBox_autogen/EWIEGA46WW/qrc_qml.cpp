/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 5.14.2
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // /root/PhoneBox/ConferenceScreen.qml
  0x0,0x0,0x3,0x97,
  0x0,
  0x0,0x26,0xbd,0x78,0x9c,0xed,0x57,0x4d,0x6f,0xd3,0x40,0x10,0xbd,0xe7,0x57,0xac,
  0xcc,0xd,0x5a,0x37,0x1f,0x4d,0x5a,0x72,0x2b,0x41,0x40,0xa5,0x5e,0x1a,0xa0,0x17,
  0xc4,0xc1,0x71,0x36,0xc9,0xaa,0xfe,0x88,0xec,0x35,0x85,0xa2,0x48,0x15,0xa8,0x2a,
  0x2,0xb5,0x41,0x2,0x21,0xa8,0x54,0x21,0x4,0x48,0x3d,0xa0,0xb6,0x12,0x7,0x40,
  0xc0,0xbf,0x69,0x42,0xfa,0x2f,0xd8,0x75,0x62,0xa7,0xb6,0xd7,0x76,0xa8,0xb,0xe6,
  0xb0,0x2f,0x52,0xe4,0x9d,0x99,0x9d,0xec,0x8e,0xdf,0xbc,0xdd,0x20,0xb5,0xad,0x1b,
  0x18,0x2c,0xe3,0x65,0xb,0xc9,0xab,0x20,0x2f,0xe6,0xf2,0x19,0xe4,0xb1,0x89,0x15,
  0x5d,0xc3,0x86,0xae,0x98,0x4c,0xe7,0x92,0xf4,0x40,0xb7,0xb0,0x9,0x72,0xd4,0x97,
  0xa9,0x42,0x19,0x4b,0x5a,0x53,0x81,0xe0,0x61,0x6,0x10,0xc8,0xba,0xa2,0x1b,0x65,
  0x20,0xac,0xb5,0x10,0x86,0x82,0x6d,0xb2,0xbf,0xaa,0xfa,0xda,0x28,0x82,0x42,0xd2,
  0xe4,0x96,0x6e,0x98,0x62,0x3,0x29,0x4a,0x19,0xb4,0x25,0x3,0x6a,0x38,0xe0,0x54,
  0x25,0xa3,0x89,0x34,0xb3,0xc,0xf2,0x59,0xd7,0x67,0xb6,0x25,0x19,0x69,0x4d,0x8f,
  0xcd,0x7d,0x98,0x99,0x1,0x4b,0xb0,0x81,0x81,0x89,0xea,0x10,0x4c,0x83,0xdb,0x26,
  0x34,0x80,0x82,0xcc,0x71,0x66,0xff,0x62,0x1d,0xac,0xa1,0x3a,0x6e,0x39,0xeb,0x10,
  0xed,0x11,0xb8,0x8,0xb2,0x62,0xc9,0x13,0xd5,0x82,0xa8,0xd9,0xc2,0x6e,0xd8,0x70,
  0xe8,0x89,0x70,0x36,0x8f,0xd,0x49,0x33,0x87,0x61,0x82,0x27,0xc0,0x33,0xa8,0xe8,
  0x8a,0xa5,0x6a,0xbe,0xa5,0x9c,0x2e,0x0,0xf9,0x42,0xeb,0xe4,0x55,0x48,0x4a,0x85,
  0x64,0x82,0xc6,0xf8,0xa7,0x7d,0x8e,0x40,0x6,0xb7,0x4c,0x85,0x6c,0xc0,0x17,0x30,
  0xdc,0x82,0xf7,0x31,0x63,0x15,0x14,0x98,0xb8,0xc8,0x7e,0x8e,0x7f,0xec,0xe,0xe,
  0xe,0x7a,0xdd,0x47,0xc7,0x5f,0x77,0x6,0x1b,0x9b,0x2,0x33,0xb6,0x41,0x16,0x24,
  0xb6,0x75,0xa4,0xe1,0x9b,0x68,0x1d,0x96,0x41,0xae,0x14,0x1e,0x56,0xd3,0x95,0x7a,
  0x19,0x60,0xc3,0x82,0xcc,0x98,0xe4,0x5,0xe8,0xc4,0x6f,0xfb,0xba,0x81,0xea,0x21,
  0xdb,0x4e,0xfe,0xfb,0x14,0xb2,0xfd,0x7e,0x9,0x7f,0xb,0x4c,0x77,0xd4,0x3b,0x62,
  0x2e,0x98,0xa2,0xa,0xdb,0x50,0x22,0x3f,0x18,0xb2,0x70,0xa,0x55,0xaf,0x43,0xd2,
  0x54,0x77,0x42,0x3,0x28,0x1e,0x6a,0x92,0x4a,0xde,0x91,0xf0,0xeb,0xe5,0x7e,0xff,
  0xc9,0x97,0x9c,0x30,0x5,0x34,0x4b,0xad,0xd1,0x1d,0xa,0x73,0x73,0xd9,0x59,0x51,
  0x14,0x85,0xce,0xd4,0x1f,0x64,0xc8,0x27,0xce,0x50,0x48,0x9c,0x61,0x36,0x71,0x86,
  0x62,0xe2,0xc,0xa5,0xc4,0x19,0xe6,0x12,0x67,0x98,0x4f,0x9c,0xe1,0x32,0x33,0x43,
  0x68,0x82,0xbb,0xa1,0x9e,0x50,0x47,0x98,0x12,0xfb,0x31,0x52,0xe6,0x5c,0x96,0xdd,
  0x23,0xe,0x1c,0x69,0x9e,0x8f,0xe,0x73,0xf4,0xf9,0x42,0xa3,0x48,0x3f,0x6c,0x1d,
  0x73,0x50,0xd3,0x8d,0x3a,0x34,0x44,0x77,0x8e,0x6c,0x63,0xa2,0x39,0xce,0xaa,0x23,
  0x63,0xd,0xa9,0x8e,0x2c,0xa2,0xe,0xc5,0xc8,0xa8,0x48,0x67,0xe8,0x9,0xe2,0x87,
  0x23,0x68,0xb2,0xad,0x56,0x8b,0x5a,0xe0,0xcc,0xd,0x83,0x2b,0x52,0xd1,0xab,0x8c,
  0x5d,0x29,0x45,0xc4,0x39,0xe3,0xc7,0xf0,0xdc,0xb1,0x95,0xec,0xaa,0x84,0x25,0x91,
  0xb2,0x73,0xa2,0x89,0x81,0x43,0x28,0x3f,0xf9,0xb4,0x98,0x43,0xc9,0x8f,0xf3,0x39,
  0x24,0x4e,0x23,0xbc,0xc3,0x1c,0xfc,0xd5,0x1a,0xdb,0xd,0x7f,0xb6,0x2a,0x47,0xb7,
  0x9d,0x3,0xb7,0x95,0x4a,0x36,0xa2,0x5b,0xc9,0xc1,0xbf,0x2e,0x73,0xb8,0x97,0xed,
  0x9,0x5a,0xbd,0x96,0xf1,0x68,0xfc,0x74,0xfa,0xc6,0x5a,0xa5,0xca,0xe5,0x5c,0x59,
  0x47,0x17,0x6f,0x50,0xb3,0x30,0xd6,0x35,0xf3,0xcc,0x17,0xd7,0x82,0xb7,0x5d,0xcf,
  0xfb,0xe6,0x1a,0x72,0x75,0x9a,0x58,0x65,0xdc,0x9b,0x51,0xb0,0x3b,0x5d,0xc1,0xc9,
  0x5,0x15,0x27,0x60,0x20,0xe5,0x5b,0xa1,0xa9,0x20,0xc9,0x38,0xfc,0xc7,0x12,0x8,
  0xb9,0x62,0x57,0x32,0xa4,0x25,0x1c,0x9d,0xce,0xb3,0xf9,0xeb,0x94,0xad,0xc4,0x76,
  0x8f,0x2e,0xc7,0x27,0x6f,0x3f,0x9f,0x6c,0x75,0xa7,0xd9,0x6c,0xae,0x49,0xf2,0x6a,
  0xd3,0xd0,0x2d,0x8d,0x28,0xcb,0x24,0x67,0x1e,0xe3,0xef,0x13,0x33,0xad,0xef,0x6c,
  0x9a,0xad,0x2c,0x5c,0x2b,0x66,0x63,0xe3,0x47,0xfb,0xd,0xd7,0xc4,0xe8,0x33,0x89,
  0xdd,0x0,0xb4,0xf4,0xe4,0x25,0x2f,0x62,0xa8,0x96,0xe3,0xf4,0x67,0x58,0xb2,0x11,
  0xd,0xe9,0x20,0xb6,0x10,0xb1,0x5b,0x1b,0xb7,0xff,0x82,0x82,0x9a,0x9a,0x4a,0x32,
  0xf,0x97,0x21,0xda,0xe3,0x1b,0x31,0xa2,0x70,0xf,0x1a,0x18,0xc9,0x21,0x93,0x57,
  0x22,0x26,0xc7,0xf5,0x3d,0x45,0x6a,0x6c,0xbc,0xc4,0xd9,0xc8,0xd9,0xe8,0xb7,0xb0,
  0xe4,0xb3,0x4a,0xb4,0x36,0x55,0xf1,0x7c,0xf1,0xb8,0xf7,0xfe,0x88,0x8b,0x27,0xa7,
  0xeb,0x7f,0x21,0x9e,0x36,0x1b,0xb9,0x78,0x72,0x36,0x4e,0x24,0x9e,0x15,0x49,0x51,
  0x52,0x15,0xcf,0xbd,0x37,0x27,0xdf,0x5f,0x73,0xb6,0x72,0xb6,0xfa,0x2d,0x29,0x90,
  0xb1,0xf7,0x61,0xb7,0xff,0xea,0xdb,0xc9,0xc6,0xee,0xe0,0x70,0x8f,0x53,0x92,0x53,
  0xd2,0x6f,0x49,0x43,0x1f,0xb7,0xb6,0xfb,0xcf,0xf6,0x39,0x19,0x39,0x19,0xfd,0x96,
  0x14,0xc8,0x38,0xf8,0xf9,0xa9,0xbf,0xf3,0x91,0x93,0x91,0x93,0xd1,0x6f,0x49,0xe3,
  0xb0,0xde,0xdc,0xee,0x77,0x9f,0x73,0x32,0x72,0x32,0xfa,0x2d,0x69,0x28,0xe3,0xe1,
  0x5e,0xef,0xe9,0xbb,0x5e,0xf7,0x88,0xf3,0x91,0xf3,0x91,0x3d,0x1a,0x3e,0x75,0x32,
  0x9d,0xdf,0x86,0x24,0x7e,0xf3,
    // /root/PhoneBox/VideoScreen.qml
  0x0,0x0,0x2,0x5f,
  0x0,
  0x0,0x15,0x22,0x78,0x9c,0xed,0x56,0x4d,0x6b,0xd4,0x40,0x18,0xbe,0xef,0xaf,0x18,
  0xd2,0x8b,0x82,0xa4,0x9b,0x6c,0x5b,0x34,0x37,0xbb,0xe0,0x7,0xe8,0xa1,0xad,0xe0,
  0x41,0x3c,0x64,0x93,0xd9,0x64,0x68,0x32,0x13,0x26,0x13,0xb6,0x2a,0x7b,0xf1,0xe0,
  0xb5,0x15,0x41,0x41,0xa,0x8a,0x7,0xbd,0x28,0x28,0x1e,0xfc,0xc0,0x5f,0x23,0xee,
  0xa2,0x37,0x7f,0x82,0x33,0xb3,0x49,0xdc,0x64,0x92,0x18,0xda,0x1e,0x4a,0xcd,0x73,
  0x58,0x32,0xf3,0x3e,0xbb,0xfb,0x7e,0x3c,0x79,0x66,0x50,0x18,0x11,0xca,0xc0,0x16,
  0xdb,0x4a,0x90,0xb3,0xb,0x4c,0xdd,0x30,0x7b,0xa8,0xb0,0xa7,0xf,0x9,0x66,0x94,
  0x4,0x71,0x65,0xf0,0x86,0x7d,0x8f,0x24,0x2c,0x6,0x86,0x88,0xf5,0xb6,0xa1,0xc3,
  0x6c,0xec,0x5,0x10,0x3c,0xe8,0x1,0xe,0x87,0x4,0x84,0x5a,0x40,0x9b,0xf8,0x88,
  0x41,0x4d,0x6e,0xc9,0x8f,0xab,0x14,0xb9,0x8b,0x6f,0xa6,0x44,0x1,0x1b,0x3b,0x3e,
  0xa1,0xb1,0x3e,0x46,0x41,0x60,0x81,0xc8,0xa6,0x10,0x33,0x25,0x18,0xda,0xd4,0x43,
  0x38,0xb6,0x80,0xd9,0xcf,0x63,0xfc,0x5f,0x92,0x50,0xee,0xe5,0x5b,0x94,0x4c,0x76,
  0x22,0xdb,0x41,0xd8,0xab,0x60,0x56,0x45,0xf2,0x87,0xd5,0x55,0x70,0xd3,0x46,0x18,
  0xc4,0xe,0x85,0x10,0x83,0x73,0x8c,0x44,0x20,0x80,0x63,0x76,0x3e,0x67,0x94,0x8b,
  0xcc,0xb0,0x28,0x48,0xa6,0x7f,0x1b,0xb9,0xcc,0xb7,0x0,0xa3,0x9,0xec,0x55,0x84,
  0xaf,0x41,0xe4,0xf9,0xac,0x14,0x5f,0xee,0xd7,0xca,0x40,0x42,0x2b,0x4,0x47,0x84,
  0xba,0x90,0xea,0x39,0x67,0x43,0xa2,0x92,0x33,0x59,0xfc,0xbf,0x59,0x88,0x15,0x16,
  0x43,0xd9,0x89,0x52,0x5,0xcb,0x8d,0xe6,0x75,0x67,0x43,0x10,0xcf,0xb5,0x3c,0xd1,
  0x9b,0x9c,0x28,0x16,0xb5,0xcc,0x7c,0x74,0x46,0x5f,0xe1,0x28,0x1b,0xb7,0xe0,0x1e,
  0xab,0xc8,0x4e,0x80,0xf1,0x10,0xaf,0x7f,0x7e,0xf8,0x76,0x76,0xf8,0x5e,0xab,0xa4,
  0x54,0xe8,0xae,0x8c,0x31,0x57,0xb5,0x1e,0x11,0x84,0xd9,0xe,0xba,0xf,0x79,0x52,
  0xa6,0x42,0x9b,0x9e,0x48,0x9a,0xcf,0x1f,0xce,0x9f,0xbd,0xfc,0xf9,0xe6,0xd1,0xaf,
  0x57,0x8f,0xe7,0x5f,0xf,0xc0,0xf7,0xa7,0xdf,0x9a,0x73,0x5e,0x59,0x1b,0x5e,0xbe,
  0xb2,0xde,0x6f,0x99,0xb5,0xda,0xca,0x62,0xd6,0xd3,0x7a,0x9,0xd4,0xe9,0x58,0x20,
  0x9b,0x99,0xc3,0xa7,0xa,0xe9,0x75,0xac,0xbc,0x8f,0x19,0x52,0xa5,0x5d,0x54,0xf3,
  0xf0,0x53,0x91,0x6f,0xa8,0xa1,0x46,0x9,0xb,0x50,0xdb,0x45,0x9,0x57,0xca,0xfa,
  0xb1,0x26,0xd0,0xba,0x8,0x81,0x74,0x5c,0xbf,0x5f,0x3c,0xf9,0xf2,0x8f,0xf9,0x5c,
  0x92,0x68,0x37,0x1f,0x73,0xad,0xf5,0x7c,0xfe,0x3e,0x2d,0x7b,0xd1,0xb6,0x68,0x62,
  0x6a,0x46,0xc7,0x75,0xa0,0x12,0xa5,0x73,0xa1,0x32,0xe7,0x8,0xaf,0xf7,0x8f,0xcf,
  0xef,0x66,0x1f,0xf6,0x3b,0x17,0x6a,0xce,0xba,0xc1,0x85,0x86,0x24,0x1c,0x91,0x4d,
  0xb2,0x77,0x2,0x22,0xa0,0xb,0x3d,0xa7,0x4c,0xb9,0x3a,0x92,0xc,0x52,0xdd,0x1a,
  0x66,0xbd,0xa9,0xd,0xd4,0x50,0x48,0x5c,0xc8,0xef,0x2d,0x77,0xb4,0xbc,0xcb,0x7d,
  0x43,0xbb,0x0,0x96,0x96,0x66,0x71,0x39,0xd0,0xee,0x2a,0x3f,0x32,0xb2,0x9d,0x5d,
  0x8f,0x92,0x4,0xbb,0x56,0xa3,0x3f,0xb,0xb4,0x90,0x57,0xf9,0x65,0x75,0x24,0x1a,
  0xb9,0x59,0xf1,0x95,0x9c,0xcc,0x97,0x7,0xdd,0xa9,0x73,0xa6,0x4f,0x9d,0x4d,0xc2,
  0x18,0x9,0xe5,0xbd,0xb7,0x3b,0x7b,0x4e,0xed,0xd9,0x33,0xfb,0xf4,0xba,0x3b,0x7b,
  0xba,0x1b,0xf0,0xff,0xe1,0x45,0xb4,0xbb,0x8,0x9f,0x6e,0x33,0xda,0xff,0xd8,0x99,
  0x51,0x67,0x46,0x67,0xcc,0x8c,0xa6,0xbd,0xe9,0x1f,0x3f,0x96,0xd5,0xd,
    // /root/PhoneBox/PhoneBoxApp.qml
  0x0,0x0,0x7,0xa0,
  0x0,
  0x0,0x40,0x2b,0x78,0x9c,0xed,0x5b,0x5b,0x6f,0xdb,0x36,0x14,0x7e,0xef,0xaf,0x20,
  0x52,0xc,0xd8,0x25,0x75,0xe5,0x5b,0xd0,0xa,0xeb,0x86,0x24,0xc3,0xd0,0x0,0xed,
  0x90,0xc6,0x41,0xf7,0x30,0xec,0x41,0x96,0x69,0x5b,0x88,0x2c,0x19,0x32,0x9d,0xf4,
  0x82,0x0,0xed,0xcb,0x30,0x74,0x3,0xba,0x62,0xde,0x8a,0x75,0x1d,0xba,0xee,0xa5,
  0xc5,0xba,0x26,0xdb,0x43,0x8b,0xa2,0x97,0xed,0xbf,0xc,0xb1,0x93,0xbd,0xed,0x27,
  0x8c,0xa2,0x48,0xd9,0x12,0x49,0x99,0x72,0x1d,0x37,0x59,0x7c,0x2,0xb4,0x12,0x79,
  0x28,0x92,0xe7,0x7c,0x3c,0x3c,0x87,0x3c,0xb6,0x1a,0x4d,0xd7,0x43,0xe0,0x2,0xba,
  0xd0,0xb6,0xcc,0x35,0x90,0xcb,0x64,0x73,0xc7,0xac,0x48,0x59,0xe6,0x53,0xcb,0xa9,
  0xb8,0x1b,0xc2,0xaa,0x45,0xd7,0x41,0x9e,0x6b,0xb7,0x84,0x95,0xe7,0x8c,0xcb,0x6e,
  0x1b,0xb5,0x40,0xd6,0xaf,0x3b,0x36,0xdf,0x6c,0xda,0x96,0x69,0x20,0xcb,0x75,0xe8,
  0x7,0xaf,0x1e,0x3,0x98,0xac,0x8a,0xe,0x1a,0x86,0x45,0xb,0x49,0xd1,0xba,0xd5,
  0xb2,0xca,0x36,0xd4,0x1,0xf2,0xda,0x90,0x94,0x6c,0x58,0x15,0x54,0xd7,0x41,0xb6,
  0x50,0xd0,0xc8,0x7b,0x1d,0x5a,0xb5,0x3a,0xd2,0xc1,0x69,0x2d,0x78,0x47,0x16,0xf2,
  0xf9,0x67,0x96,0xeb,0xae,0x3,0x17,0xdc,0x4b,0x33,0xa4,0x94,0xfc,0xd3,0xf4,0xdc,
  0x26,0xf4,0xd0,0x65,0x60,0x39,0x8,0x98,0x6d,0xcf,0x83,0xe,0x5a,0x35,0xca,0x3a,
  0xd0,0xc0,0xc9,0x93,0x40,0xd3,0x71,0x6f,0x15,0xe8,0xce,0x82,0xac,0xe,0x9a,0x7e,
  0xeb,0x59,0x90,0xd3,0x81,0xe9,0x3a,0x55,0x88,0x39,0x4d,0xfc,0x9a,0xd7,0x41,0xb,
  0x19,0xa8,0xdd,0x9a,0x5,0x5,0xfc,0x8,0x11,0xb2,0x9c,0x5a,0xab,0xdf,0x1,0xfe,
  0xca,0xaa,0xdb,0xc4,0x43,0x32,0x2a,0xd0,0xc3,0x23,0x45,0x75,0xe0,0x40,0xb4,0xe1,
  0x7a,0x6b,0xb4,0x1d,0xe1,0x5a,0x81,0x26,0x32,0x9c,0x9a,0xd,0xe9,0xb4,0xd9,0xd4,
  0x83,0x66,0xb,0x86,0x17,0x96,0x1a,0x8e,0x59,0x77,0xbd,0x56,0x6,0xb9,0x4d,0x3c,
  0x24,0xc3,0x1f,0xaf,0xff,0xcc,0xd5,0xdb,0xb0,0x8a,0x42,0x6,0xff,0x85,0xe3,0xf0,
  0x2,0x21,0x51,0x16,0xf2,0x16,0xf2,0x30,0x9,0x9e,0xd2,0xc2,0x22,0xd3,0xb5,0x5d,
  0xf,0xb,0xf1,0xb8,0x59,0xc8,0xc2,0xbc,0x31,0x13,0x56,0x84,0xf,0x2b,0xa1,0xd6,
  0xd4,0x7,0x33,0xc8,0xb5,0x8e,0x15,0x81,0x31,0x60,0x2f,0x62,0x2e,0xe8,0x85,0xfc,
  0xd1,0x62,0xe9,0xf7,0xcf,0x1b,0x5e,0xcd,0x72,0x74,0x90,0xd3,0x22,0x2c,0xad,0xa6,
  0x61,0x62,0x8d,0xe8,0x20,0x1f,0x2d,0x8f,0xbc,0xac,0xc0,0x26,0x34,0xf0,0xc7,0x63,
  0xc3,0xf7,0xa9,0xe1,0x56,0xa0,0xad,0x83,0xcf,0xb8,0xa,0x9f,0xae,0x3a,0x46,0xc3,
  0x7,0xd6,0xce,0xb3,0xc7,0xdd,0x1f,0x5f,0xed,0x5d,0xef,0xfc,0x73,0xed,0xce,0xcc,
  0x2c,0x68,0x59,0x35,0xc7,0xc0,0x8d,0xb2,0x9b,0xb3,0xa,0xcd,0x76,0x3b,0x4f,0x76,
  0xfe,0xba,0x3f,0xd0,0x2c,0xa7,0xd6,0xec,0xc1,0x8b,0xee,0x8d,0x87,0x3,0xcd,0xf2,
  0xa3,0x35,0x2b,0x28,0x35,0xe3,0xe6,0x56,0x1c,0xd2,0xac,0x77,0xf7,0xd7,0xee,0x37,
  0xdb,0xbb,0x9d,0xc1,0xae,0xb4,0x4d,0xae,0xc9,0xe7,0x5c,0x9,0x57,0x20,0x5a,0x1b,
  0x83,0xc4,0x56,0xbf,0xa6,0x9,0xab,0x19,0x94,0xe7,0xc4,0xd5,0xc,0xd6,0xc8,0x33,
  0x9c,0x56,0x80,0xb8,0x19,0x21,0xa3,0xb0,0x70,0xd1,0xb5,0xdb,0xd,0x47,0x32,0x30,
  0x9f,0x18,0x42,0x4d,0x2,0xde,0x25,0x87,0xa1,0x5a,0xda,0x20,0xc4,0x6b,0x51,0xca,
  0x22,0xad,0x18,0x26,0x29,0x46,0x54,0x62,0x5,0xb1,0x44,0x18,0x31,0xc1,0x65,0xe5,
  0x23,0xf1,0x89,0xa,0x90,0xac,0x93,0x8f,0xc,0x64,0x64,0x2,0x6d,0x83,0xf,0xb0,
  0x9,0xfd,0x10,0x9b,0x8b,0xc2,0xe2,0xfc,0xc7,0x45,0x6d,0x6,0xf8,0xa6,0x63,0x8e,
  0x90,0x58,0xbe,0x8c,0x3c,0xa3,0x62,0xb5,0x5b,0x78,0x15,0x24,0x72,0x25,0x56,0xf2,
  0x86,0x48,0x44,0xa9,0x55,0xc3,0x28,0x54,0x51,0xf2,0x10,0xc9,0x48,0xe4,0x86,0x45,
  0x44,0xd4,0xd8,0x24,0xb,0xbc,0xff,0x6d,0x35,0x6d,0xf,0x12,0xd5,0x7c,0x5e,0xb9,
  0x41,0xb8,0x7a,0xc0,0x7b,0x78,0x8b,0xac,0xc0,0x4b,0xe0,0x5d,0x85,0x69,0x33,0xa2,
  0xd8,0x8,0x1a,0xbe,0xcf,0x63,0x4,0xe3,0x63,0xa3,0x6e,0x21,0x48,0xd0,0x31,0x74,
  0x5,0x8a,0xa8,0xec,0x7a,0x78,0x83,0xcc,0xb0,0x55,0x1c,0x7c,0x2d,0x6d,0x6b,0x66,
  0x41,0x94,0xda,0xf1,0x36,0x2c,0x1d,0x87,0xbc,0x56,0x5e,0x23,0xad,0x58,0x85,0x97,
  0xd0,0x10,0xf5,0x23,0xcc,0x32,0xb8,0x3c,0x7d,0xfb,0xac,0xb2,0xa0,0x55,0x64,0x59,
  0xc5,0xe,0x5e,0xa6,0xe9,0x62,0xdf,0xa9,0x64,0x5d,0xc1,0x56,0xff,0x54,0x22,0x37,
  0x5b,0x70,0xf8,0x1f,0xeb,0xa,0x6e,0xc9,0xed,0xf4,0xf1,0x8a,0x94,0x92,0xe2,0x4b,
  0xa3,0x25,0xfd,0xb7,0xfe,0x53,0xf8,0x80,0xfd,0xb4,0x15,0x1f,0xea,0x78,0xb3,0xaa,
  0x40,0xea,0x9a,0x61,0xe0,0x56,0x5d,0x5,0xf7,0x26,0xc9,0x93,0x1a,0xe4,0x1b,0xdd,
  0xc1,0x21,0xdf,0x1c,0xe6,0xe1,0xe4,0x12,0x3c,0x1c,0x9,0x50,0x2,0x70,0xcc,0x74,
  0xbf,0xbe,0xbd,0xf3,0xfc,0x37,0xec,0x40,0xeb,0x5a,0x8e,0xd7,0xf8,0x10,0x40,0xc4,
  0x41,0x90,0xcd,0x49,0xa4,0x9e,0x76,0x50,0xaf,0xbe,0xed,0xde,0xfd,0x1d,0x7b,0xd,
  0x73,0x93,0x1d,0x55,0x92,0x5d,0xa5,0x96,0xe2,0x14,0xbf,0x73,0x32,0x43,0x99,0xe7,
  0xab,0x42,0xcf,0x99,0x6e,0x85,0x1c,0x3,0xdb,0xf2,0x4,0x3b,0x2d,0x57,0x90,0xb0,
  0xe2,0x53,0x6d,0x68,0x54,0xc8,0xd8,0x6b,0x7a,0x4b,0xbc,0xc6,0x15,0xcc,0x0,0x27,
  0x63,0x7e,0xee,0xb2,0x15,0xc8,0xcd,0x4d,0x41,0xea,0x2,0xf,0xee,0x90,0x4a,0xbd,
  0xfb,0xc7,0xb5,0xdd,0x47,0xdb,0x6f,0x42,0xee,0xc1,0xd3,0x66,0x24,0x3e,0x3d,0x87,
  0xa3,0x27,0x62,0xf6,0xca,0x6,0x8d,0x50,0x2b,0x70,0xdd,0x32,0x21,0xb0,0xad,0x16,
  0x4a,0xc,0x4f,0xfd,0xb8,0xab,0x14,0x34,0x14,0x7,0xa8,0x61,0xfc,0x9a,0x29,0xbb,
  0x8,0xb9,0x8d,0x11,0xc2,0xd4,0xa0,0x61,0xc8,0x13,0xfb,0xe,0x85,0x46,0x4e,0x13,
  0x4,0xaa,0xd5,0xa2,0xff,0xd7,0x17,0x61,0xcc,0x57,0x38,0x5e,0x21,0xc4,0xd5,0x73,
  0xde,0x40,0xf8,0x20,0xf4,0xf6,0xd9,0x38,0xab,0x96,0x6d,0xb,0x75,0xcf,0x18,0x1a,
  0xc4,0x7c,0xb7,0x38,0x75,0x85,0xf6,0x3b,0x3b,0xb2,0xfd,0x3e,0x37,0xff,0x9,0xc8,
  0xca,0x4d,0xe4,0xf1,0xd3,0x84,0x14,0x8c,0xa4,0x26,0x81,0xd,0x37,0x9e,0x85,0x36,
  0x56,0x83,0x28,0xf0,0xa1,0xd2,0xa3,0xda,0x22,0x6f,0xe0,0x44,0x7c,0x6b,0xf2,0x89,
  0x2d,0x5d,0x41,0x14,0x42,0x67,0xb5,0xf7,0xe0,0x8b,0xbd,0xad,0x6d,0x4d,0x30,0xb1,
  0xb2,0x61,0xae,0xd5,0x3c,0xb7,0xed,0x60,0xc,0xe,0x73,0x83,0x87,0xda,0x1,0xf2,
  0xc1,0x18,0x34,0x14,0x78,0x19,0xf2,0x84,0x3c,0xcc,0xb6,0xf0,0xa6,0x85,0x77,0x52,
  0x4c,0xac,0x4,0x2c,0xab,0x25,0x4,0x31,0xca,0x13,0xc,0x4d,0x20,0x15,0x76,0xe4,
  0x83,0x5f,0x46,0x35,0x1f,0x7d,0x37,0x6b,0xde,0xc6,0x7e,0x78,0x3,0x7f,0x2f,0xe8,
  0x38,0x43,0xde,0xcf,0x26,0x38,0x5f,0xcc,0x55,0x11,0x36,0xbc,0x28,0x69,0xa8,0xbc,
  0x7,0x4c,0x10,0x54,0x2,0x87,0x62,0x14,0x50,0x25,0x48,0x39,0xe,0x29,0x93,0x90,
  0xa,0xa4,0xc4,0x71,0x88,0x2a,0xa4,0x46,0xf6,0xb7,0x7c,0x23,0x92,0xe0,0x67,0x1d,
  0x52,0x23,0x72,0x36,0x7f,0x62,0x5c,0x16,0xe4,0x70,0x29,0x7b,0x52,0xc2,0x3d,0x92,
  0x2b,0x69,0x12,0xc2,0xbd,0x58,0x5a,0x9d,0x42,0x37,0x4a,0xe3,0x96,0xee,0x98,0xb0,
  0x3b,0x75,0x2d,0x18,0x1d,0x14,0xd7,0x42,0x14,0xe6,0x9c,0x37,0x2c,0x7,0x38,0xc6,
  0xba,0x55,0x23,0xd7,0x8c,0x0,0x19,0xe5,0xe4,0xcb,0x37,0xcc,0x20,0xbd,0x79,0x53,
  0xc,0x6c,0x6,0x2,0xa4,0xd8,0xb9,0x50,0x9a,0x5b,0xb8,0x22,0x1f,0xdc,0xc4,0x74,
  0xf0,0xda,0xa1,0x8d,0xfc,0x70,0x2b,0x31,0xae,0xd,0x63,0x97,0xa4,0xd0,0x5,0x8b,
  0x71,0xc8,0x82,0x16,0xdd,0xdd,0x8,0x26,0xcf,0x88,0x2e,0xe1,0xa5,0x65,0x81,0xb7,
  0x52,0x87,0xe6,0x1a,0xc4,0xca,0xeb,0xdf,0xe5,0x82,0x33,0x67,0xce,0x80,0x13,0xbc,
  0xb1,0x1a,0x61,0xa5,0x53,0x2d,0xd1,0x4e,0xc8,0xf5,0x46,0x55,0xf3,0xff,0xc8,0x1,
  0x76,0xa,0x23,0x1b,0xd7,0x8f,0x80,0x57,0x6a,0x64,0xd5,0xdd,0xbd,0xfd,0x12,0x7c,
  0xef,0xe1,0xfd,0xde,0x57,0xf7,0x52,0x8,0x9f,0xb7,0x64,0x53,0xe1,0x8f,0x2a,0x7c,
  0x1c,0xc0,0xfc,0xf3,0xcb,0x2d,0x75,0xe1,0xf3,0x1f,0x72,0x9d,0x45,0xdb,0xe2,0x79,
  0x5,0x9c,0xe3,0xd1,0x12,0x4d,0x18,0x78,0x63,0x5a,0xa,0x46,0xb7,0xf,0x3b,0x21,
  0x3f,0xd7,0xfe,0x6d,0x56,0xd9,0xc6,0xb2,0x3b,0xd8,0x9b,0xa4,0x4f,0x93,0xc1,0xec,
  0x6e,0xe7,0xc9,0xde,0xf6,0x4f,0xea,0x98,0xe5,0x35,0x2a,0xc3,0xec,0x7e,0x99,0xf5,
  0x29,0x66,0xa3,0x74,0x54,0x31,0xbb,0xf3,0xf2,0xce,0xde,0xd6,0x96,0x3a,0x72,0xf9,
  0x9d,0x4e,0x86,0xdc,0xfd,0xda,0x13,0xa7,0xc8,0x8d,0xd2,0x11,0x44,0xee,0x8d,0xa7,
  0xbd,0x6b,0xd7,0xd5,0x31,0xcb,0x67,0x83,0xc8,0x30,0xcb,0x73,0x4e,0x31,0xfb,0x7f,
  0xc6,0xac,0x38,0x4f,0xa2,0x44,0x93,0x5c,0x41,0x39,0x80,0xaf,0x1f,0x4b,0xd7,0x21,
  0x88,0xc6,0xae,0x42,0x68,0x1f,0x8c,0x84,0x9,0x71,0xfa,0x9b,0xe4,0xc8,0x88,0xae,
  0xa9,0xbf,0xef,0xfc,0x10,0x55,0x95,0x2a,0xf0,0xa5,0x69,0xb3,0x8c,0xc4,0xe7,0x39,
  0x51,0x8d,0xa8,0x60,0x73,0x38,0x2e,0xd3,0x66,0x4c,0xcc,0xf1,0xb6,0x67,0x34,0x5c,
  0xa6,0xc6,0x64,0x74,0xf6,0x32,0x6b,0x54,0x88,0xe1,0x54,0x70,0xdc,0x43,0x5,0x7,
  0xb0,0x54,0xc,0x52,0x21,0x3b,0xeb,0xa1,0x8c,0xf3,0x8c,0xcf,0xa7,0xc8,0x81,0x4f,
  0x70,0x18,0xb4,0x9f,0xa7,0x3d,0xf1,0xb,0xef,0xe0,0x7f,0xbf,0xd3,0xc1,0xfc,0x6e,
  0x89,0x12,0xc3,0x87,0x12,0xc2,0xc0,0xc,0x72,0xeb,0xd3,0x5e,0x54,0x53,0xe1,0x2e,
  0xf9,0xd9,0x7a,0x83,0xa2,0x96,0x6f,0x61,0x58,0xcc,0x17,0xfd,0xfc,0x78,0x5f,0x3a,
  0xe0,0x6d,0xed,0x9d,0x48,0x25,0xa9,0x29,0x99,0x1e,0x84,0xa2,0x1d,0xce,0x17,0xfa,
  0x7a,0x9f,0x43,0x75,0xd3,0xc4,0x3d,0x92,0x2c,0xfe,0xa0,0xc7,0x6c,0xb4,0x47,0x52,
  0x93,0xd8,0x63,0xb3,0xcf,0x91,0xa2,0xc7,0xc5,0x30,0xdf,0x3f,0xe8,0x36,0x17,0xed,
  0xb6,0x5f,0x9d,0xd8,0xb7,0x19,0x63,0x4b,0x31,0x80,0x52,0x90,0x8e,0x46,0x3a,0xcf,
  0x47,0x3b,0xf,0xaa,0x12,0x3b,0x6e,0xd,0xb0,0xa4,0xe9,0x94,0x99,0x79,0xd2,0x6d,
  0x21,0xd6,0x2d,0xad,0x4c,0xee,0x38,0xc2,0x94,0xb8,0xc7,0x44,0xd7,0xee,0x2,0x81,
  0x3e,0x60,0x69,0x25,0xb2,0x45,0x1b,0xae,0x10,0xe9,0x1a,0x12,0x27,0x8d,0x8c,0xf7,
  0x37,0x12,0x73,0xfc,0xe9,0xec,0xf8,0x53,0x4f,0xe,0xf6,0x6f,0x2b,0x92,0x32,0xf,
  0x87,0xf9,0xb8,0x9,0xb9,0x74,0xf2,0xdb,0x9b,0xee,0xad,0x97,0xdd,0x9b,0x8f,0x26,
  0x70,0x7b,0x33,0xbd,0x6d,0x91,0x4e,0xf9,0x35,0x2e,0xf0,0x46,0xd3,0xf9,0xf3,0xce,
  0xee,0xe3,0xce,0x98,0x74,0x4e,0x4f,0x8e,0x95,0xe2,0x8c,0x43,0x70,0x23,0x3a,0x9a,
  0x40,0x6f,0x7e,0xdf,0x7b,0xfa,0xe5,0x91,0x10,0xa8,0x20,0x90,0x39,0x18,0x16,0x35,
  0x5f,0x1c,0xc1,0xa4,0x2a,0xe4,0xca,0xa,0x12,0x62,0x15,0x72,0x65,0x13,0x7f,0x82,
  0xc1,0x15,0x8c,0x39,0x29,0x96,0xb,0xb1,0x18,0x29,0x4,0x26,0x63,0x4a,0x81,0xda,
  0xdb,0xfa,0x73,0xf7,0x95,0xe0,0xb4,0x6f,0x74,0x9d,0xb,0xc7,0x3f,0xce,0x54,0xf4,
  0x43,0xa8,0xe8,0x7f,0xef,0x75,0x6e,0xbc,0x69,0x4d,0xe7,0x4,0x89,0x35,0x53,0x2d,
  0x8f,0x59,0xcb,0x82,0xcd,0xda,0xa7,0xc9,0x69,0x39,0x5b,0x3c,0x38,0x5a,0x1e,0x32,
  0xd2,0x82,0x9e,0xd5,0xf4,0x7c,0x16,0xec,0x3c,0x7b,0xd1,0xbb,0xfb,0x7c,0x69,0x39,
  0x30,0x45,0x4b,0xcb,0xbd,0xdb,0x3f,0xf7,0xbe,0x13,0x6c,0xd2,0x63,0x9e,0x46,0xc2,
  0xef,0x27,0x44,0x3f,0xda,0xe4,0xc3,0xb7,0xcd,0xff,0x0,0x71,0x4,0x1,0x98,
    // /root/PhoneBox/SettingsScreen.qml
  0x0,0x0,0x6,0x82,
  0x0,
  0x0,0x4a,0x2a,0x78,0x9c,0xed,0x5c,0x5b,0x6f,0x14,0x37,0x14,0x7e,0xcf,0xaf,0xb0,
  0xc2,0xb,0xa8,0x64,0x33,0x33,0xd9,0x44,0x74,0x5f,0x2a,0x8,0xaa,0x40,0x2,0x54,
  0x2e,0xa5,0xf,0x55,0x1f,0xbc,0x33,0xce,0xae,0xc5,0xec,0x4c,0x34,0xeb,0x4d,0x2,
  0x28,0xf,0xa8,0x55,0x4b,0x29,0x52,0x48,0xa9,0x8a,0x5a,0x5,0xa5,0xa8,0xa8,0xe2,
  0x1,0x41,0x24,0x50,0x41,0xa4,0xf0,0x63,0xaa,0x4e,0x36,0x79,0xeb,0x4f,0xa8,0x3d,
  0x3b,0x9e,0xec,0xc5,0x33,0xb6,0x37,0x13,0xba,0x21,0xf3,0xad,0x44,0x76,0xec,0x63,
  0xcf,0xb1,0x7d,0xce,0xe7,0xe3,0xcb,0x82,0x1b,0xf3,0x7e,0x40,0xc0,0x45,0x72,0xb1,
  0x85,0xed,0x6b,0xc0,0x2a,0x99,0xd6,0x18,0xee,0x49,0x2b,0xcd,0xfa,0x1e,0x9,0x7c,
  0xb7,0x29,0xcc,0x3c,0x7,0xaf,0xfb,0x2d,0xd2,0x4,0x26,0xcb,0x1b,0xbb,0x84,0x6c,
  0x2,0xbd,0x9a,0x8b,0xc0,0xcd,0x31,0x40,0x61,0xfb,0xae,0x1f,0x54,0xc0,0xf8,0x91,
  0xb9,0x69,0xf6,0x19,0x8f,0x12,0xa3,0x7f,0x2e,0xf9,0x8b,0xb1,0xc,0x3,0xf4,0xec,
  0xba,0x1f,0x34,0x4b,0x73,0xd8,0x75,0x2b,0x60,0x1e,0x6,0xc8,0x23,0x49,0x66,0xf2,
  0x65,0x72,0x12,0x9c,0x43,0x73,0x4,0x34,0xb1,0x83,0xaa,0x30,0x0,0x1e,0x5c,0xc0,
  0x35,0x48,0xb0,0xef,0x25,0x22,0xfd,0xa,0x70,0x2c,0x62,0x87,0xd4,0x2b,0xc0,0x32,
  0x8c,0x9e,0xe4,0x3a,0xc2,0xb5,0x3a,0xe1,0x6f,0x2c,0x75,0x1e,0x7b,0x24,0x92,0x16,
  0x20,0x83,0x7d,0xc6,0x7b,0x32,0x7b,0x1e,0x66,0x7d,0xb7,0xd5,0xf0,0xfa,0xde,0x2b,
  0x6d,0x9c,0xb0,0xaa,0xb8,0xb1,0x67,0x10,0x74,0x50,0x30,0x90,0x93,0xd6,0xc6,0xbe,
  0xb6,0xc6,0x6d,0x8a,0x9e,0x84,0x72,0xbc,0xf1,0x33,0x86,0x30,0x3b,0x69,0xb9,0x5d,
  0x36,0xd1,0x14,0x1c,0x17,0xa,0x9,0x13,0xaf,0xa0,0x25,0x92,0xa2,0x1b,0x3,0xef,
  0xf,0x9b,0xaa,0x87,0x82,0xb3,0x5e,0x6a,0x9f,0x70,0x10,0x5a,0x1f,0x55,0x64,0xfb,
  0xd9,0xbb,0xf6,0xdb,0x67,0x62,0x3d,0xba,0x15,0x5e,0xac,0x63,0x82,0xd2,0xc5,0xe6,
  0xa8,0x39,0x97,0xe6,0x7d,0xec,0x91,0xcb,0xf8,0x6,0xaa,0x0,0x73,0x26,0x5b,0xb4,
  0xea,0xbb,0x4e,0x5,0x90,0xa0,0x85,0x84,0x72,0xcb,0x3,0xa9,0x83,0x29,0xa2,0xc1,
  0xbd,0x90,0x18,0x2f,0xa0,0xea,0x36,0x9a,0x3,0x32,0xa9,0xf6,0xc4,0xa0,0x3a,0xc6,
  0xc2,0x44,0x99,0x1,0xe9,0xbc,0x80,0x81,0x1b,0xd2,0xb4,0xd8,0x90,0x18,0x94,0x8c,
  0x29,0x55,0xe1,0x48,0xe9,0x1e,0xc2,0x10,0x81,0xdb,0x95,0x4b,0x39,0x22,0xd1,0x9c,
  0x3d,0x28,0x95,0x5a,0x40,0x1,0xc1,0x36,0x74,0x67,0x23,0xab,0x4c,0xca,0xf7,0x26,
  0x2b,0xbf,0xff,0x3c,0xc,0x6a,0x98,0x5a,0xb6,0x39,0x9d,0x59,0xa4,0x39,0xf,0x6d,
  0xec,0xd5,0xa8,0x5c,0x7a,0xd7,0x31,0x64,0x66,0xaa,0xc,0x27,0x47,0x3c,0xac,0xe9,
  0x26,0xcf,0x91,0x90,0x83,0x54,0x52,0xd1,0xed,0x38,0x2,0xe8,0xe0,0x56,0xb3,0x2,
  0xa6,0xa4,0x92,0xf9,0x8d,0xcc,0xa0,0x47,0x76,0x23,0x33,0x53,0xc2,0x65,0x1c,0x31,
  0x45,0x85,0xaf,0x5f,0xb7,0x7f,0x7a,0x22,0x23,0x2a,0xe,0xcd,0x9e,0x1b,0x20,0x2e,
  0x6b,0x24,0xba,0x50,0x9c,0x23,0x4e,0x2d,0xe8,0xe8,0xff,0xa5,0xa3,0xa9,0xd1,0xa4,
  0xa3,0x13,0xca,0x74,0x24,0x97,0xd4,0x74,0xaa,0x3,0x48,0x32,0x94,0x61,0xb6,0x6e,
  0xbf,0x6a,0xff,0xf5,0xf3,0xf6,0xbb,0xd5,0xfd,0x27,0x19,0x73,0x24,0xba,0xf0,0x20,
  0x93,0xc,0x9,0xa0,0xd7,0xec,0x54,0x7a,0x68,0x88,0xe6,0x7d,0xc4,0x3d,0x7a,0x5e,
  0xf3,0xef,0xfa,0xea,0xe3,0x21,0xcc,0xbf,0x3c,0x12,0xe6,0xcf,0x90,0x63,0x5f,0x6c,
  0xbf,0xfc,0x23,0x5c,0x79,0xd5,0x7e,0xf6,0xa8,0x7d,0xef,0x5b,0x75,0x6,0x39,0x32,
  0x15,0xa1,0x8,0x54,0xa,0xe,0xe9,0x2b,0x75,0x78,0x38,0xe4,0xee,0xbd,0x82,0x43,
  0xe2,0x28,0xe4,0xed,0x6a,0x7b,0x73,0x4d,0x77,0xa9,0x53,0x70,0x48,0xc1,0x21,0xe2,
  0x52,0x87,0x85,0x43,0xfe,0xfe,0xf5,0x97,0x82,0x42,0x62,0xa,0x79,0xb1,0xd9,0xde,
  0x5c,0x6f,0x6f,0xbe,0xdc,0xba,0xa3,0x10,0x9a,0x1d,0x36,0xa,0xe9,0x4d,0xd9,0x7d,
  0xda,0xfd,0x96,0x7c,0x99,0x9c,0x4,0xe7,0x21,0xf6,0x68,0x17,0x51,0xbd,0x3c,0x2,
  0xa8,0xb6,0x30,0xc9,0x94,0x1c,0x89,0x74,0xb3,0xc,0x98,0xd8,0xc3,0x9,0x89,0x60,
  0x81,0xd9,0xf3,0xb0,0xb7,0xf3,0x11,0x2e,0xd4,0x88,0x1c,0x9b,0xed,0x1f,0xe,0x7a,
  0x6c,0xe2,0xcd,0x82,0xbc,0x81,0x4,0xda,0x67,0x57,0x60,0x15,0x54,0x5b,0x84,0xf8,
  0xde,0xe0,0xee,0x7b,0x3a,0xdd,0x25,0x6f,0xb1,0xc4,0x9c,0xb1,0x57,0xda,0x37,0x8d,
  0x74,0x2e,0xe2,0xa3,0x51,0xce,0x61,0x6b,0x8b,0x6f,0xc4,0xa6,0x33,0x64,0x6a,0x86,
  0x82,0x9b,0x6b,0x1f,0xf2,0x30,0xf0,0x5d,0x54,0xba,0x36,0xf9,0xed,0x96,0x4a,0x68,
  0xa1,0xb1,0xb9,0xa1,0xc1,0x6,0xef,0x73,0x92,0xcf,0x69,0xb4,0x95,0xe6,0xf6,0xaa,
  0x1f,0x38,0x28,0x28,0x29,0x5b,0x48,0x2c,0xcf,0x35,0x3d,0x80,0x96,0xb4,0xf5,0xe0,
  0xcf,0x9d,0x7,0x2f,0x75,0x2c,0x49,0xda,0x29,0xc,0x85,0x2d,0x1d,0x42,0x5b,0xda,
  0x59,0x7f,0xb1,0xf3,0xdd,0xca,0x41,0xb0,0x25,0xb5,0x3,0xe8,0xd9,0x38,0x58,0x68,
  0x52,0x3,0xc3,0xa2,0x29,0x30,0x97,0x3,0xe8,0xac,0x39,0x59,0xa8,0x59,0xac,0xdd,
  0x85,0x56,0xa3,0x8a,0x2,0xaa,0x1c,0x21,0xb4,0x78,0x93,0x6b,0x29,0x14,0xcf,0x54,
  0x54,0x47,0xd9,0x1e,0x85,0x33,0x96,0xe,0x7b,0x58,0x24,0xe9,0xa8,0xd2,0xa3,0x4e,
  0x4a,0xb4,0x21,0x55,0x89,0x41,0x73,0x77,0xf0,0xf9,0xc3,0xf0,0xce,0xa3,0x70,0x65,
  0xa3,0x33,0xf,0xef,0x4b,0xa0,0xcd,0x79,0x20,0x63,0xd9,0xca,0x31,0x32,0xb,0x9b,
  0x4f,0x31,0x72,0x1d,0xf5,0x43,0xad,0xfe,0x48,0x5a,0x4,0xce,0xca,0x92,0x73,0x39,
  0x86,0x78,0x70,0x4c,0x6b,0xaa,0x2c,0x1f,0x91,0x2a,0xb4,0xaf,0xd5,0x2,0xbf,0xe5,
  0x39,0x15,0xad,0xf3,0x38,0x86,0xdd,0x3b,0x5b,0x6,0xfb,0xc8,0x5f,0x16,0xbd,0xb0,
  0x7f,0x3a,0x88,0xa0,0x55,0x56,0x3a,0x35,0x74,0x43,0xfd,0x16,0x41,0xf6,0xa0,0xeb,
  0x2e,0xd3,0x18,0x3e,0x6c,0xef,0xdf,0x5a,0x7b,0xba,0xb5,0xf6,0xa6,0x70,0xfd,0x4,
  0x23,0xe3,0xfa,0x27,0xa6,0x67,0x14,0xbc,0xb1,0x70,0xfd,0x4,0xf9,0xba,0xfe,0x8,
  0x6c,0xf2,0xa6,0x77,0x50,0xe6,0x35,0xd5,0x21,0x5a,0xc0,0x76,0x47,0x70,0x3,0xd,
  0xc4,0x5f,0x29,0xe2,0x45,0x4,0x96,0xef,0x35,0xb2,0xbb,0xf,0xfe,0x79,0xf3,0x94,
  0x2e,0x5e,0xb,0x2,0x6,0xba,0x7d,0x67,0x19,0x96,0x31,0x61,0x1a,0x13,0xc6,0xc7,
  0xc0,0x2c,0x57,0x2c,0xa3,0x32,0x65,0xee,0x4b,0x2f,0x26,0x1e,0x37,0x13,0x61,0x34,
  0x6e,0x1a,0x1d,0xd6,0xa0,0x25,0x7c,0x7b,0x3f,0x5c,0xdb,0x28,0x1c,0xa6,0x3,0xbd,
  0xbe,0x3b,0xfa,0xf9,0x95,0x59,0x3a,0x54,0xd4,0x55,0x8e,0x85,0x8f,0x57,0xc2,0x27,
  0xb7,0xc2,0xc7,0xaf,0xa,0x8f,0xf9,0xd0,0x3d,0xc6,0x2a,0x87,0x1b,0x2b,0xd4,0x61,
  0xc2,0xdb,0x85,0xcf,0x30,0x5c,0x5e,0xc4,0xc4,0xae,0x2b,0xf4,0x9f,0x5d,0x47,0xf6,
  0x35,0x94,0xf5,0x8b,0x90,0x6e,0xe4,0xd7,0x36,0x69,0xb,0x18,0xb0,0xe7,0xd0,0xea,
  0x8,0x73,0x33,0xdd,0xd8,0x1f,0x37,0xe6,0x5d,0x6c,0x63,0xf2,0x45,0x67,0xe0,0x14,
  0xc6,0xad,0xbb,0xd4,0x99,0x38,0x42,0xb5,0xe4,0xab,0x19,0x86,0xa5,0x9e,0xfb,0x1,
  0x9f,0x41,0xc7,0xa1,0xe6,0xaf,0x54,0xf2,0x7a,0xdf,0x51,0x24,0x98,0x4,0x16,0x98,
  0x0,0xbb,0xf,0x5a,0x6b,0x8,0x5,0x43,0x66,0x88,0x99,0x2b,0x7e,0x71,0x6c,0x1,
  0xe0,0x13,0x4a,0x65,0x86,0x71,0x6a,0xf6,0x74,0x79,0x1c,0xe8,0x2e,0x7c,0x94,0x84,
  0x74,0x87,0x90,0x61,0xb7,0x67,0x7,0xb4,0xed,0x3b,0xe9,0xed,0xfc,0xad,0x0,0xb5,
  0x81,0x66,0xe0,0xab,0x5b,0xf9,0xca,0x8b,0x83,0x2f,0x5c,0x34,0x8a,0x24,0x43,0xa3,
  0xb6,0x1a,0x64,0xc8,0xd7,0xc9,0x38,0x34,0x2f,0x4e,0x73,0x28,0xb,0x9e,0x42,0x75,
  0xb8,0x80,0xfd,0x0,0xd0,0x55,0xd5,0x92,0xc6,0x8,0x33,0x74,0x76,0xc7,0x4f,0x7a,
  0xb8,0xd1,0xf9,0x5,0xd9,0x4d,0xe0,0xb4,0x82,0xe8,0x6b,0xb4,0xf9,0x20,0x21,0xc2,
  0x6e,0xa8,0x49,0xca,0xa5,0x8a,0xb5,0xf6,0xd0,0x2d,0xa0,0x6b,0xed,0xab,0x6c,0xfd,
  0x3c,0xb8,0xda,0x7e,0x8f,0x6b,0xed,0x8c,0xc8,0x23,0x3d,0x10,0x42,0xf3,0x8,0x52,
  0x97,0x92,0x18,0x6f,0xc3,0x77,0x90,0x5b,0x1,0x5f,0x4a,0x6d,0xe8,0xa6,0x7,0x1b,
  0x88,0x6d,0x41,0x7e,0xff,0xc3,0xd6,0xfa,0x37,0x9d,0xf3,0xb6,0xf1,0xe3,0x60,0x1,
  0xba,0x2d,0x9a,0x6c,0x99,0xcb,0xc7,0x35,0xaa,0x78,0x1a,0xfe,0xbe,0xd1,0x5f,0x85,
  0x79,0x42,0xa3,0x8a,0x9d,0xfb,0x5f,0x8b,0xaa,0x98,0xd6,0xa8,0x22,0x5c,0x59,0xdd,
  0x7e,0xfe,0x50,0x50,0x45,0x66,0xd,0x5f,0xd,0x1f,0xc5,0xc8,0x43,0x53,0x6,0xdd,
  0xf0,0x94,0x41,0x35,0x44,0x95,0xaa,0xc8,0xa0,0x18,0xaa,0x32,0x74,0xc2,0xd5,0xc8,
  0x84,0x4e,0x43,0x2,0x4b,0xac,0x67,0x95,0xa,0xe,0x11,0xb3,0x32,0x68,0xc4,0xad,
  0xc,0xf9,0x4e,0x3d,0x72,0x9a,0x95,0xa,0xa8,0x59,0x0,0x83,0xea,0xdd,0x47,0x8e,
  0xfc,0x67,0x59,0x25,0xa1,0x53,0xd1,0x5,0x2d,0x8d,0xf9,0x31,0x1e,0xc1,0x94,0xf3,
  0x66,0x11,0x92,0xad,0x78,0xf5,0x22,0xfc,0x32,0xe7,0xed,0x1f,0xd5,0x23,0x83,0xbd,
  0xec,0xcc,0x73,0x68,0x5d,0x2d,0xe8,0x47,0x12,0x5a,0xa9,0x5,0xe9,0xc,0xea,0x81,
  0x44,0x7c,0xf7,0xf0,0x2c,0x41,0x8d,0x8a,0x8e,0x83,0x73,0x74,0x7a,0x34,0x36,0x26,
  0xf6,0x30,0x54,0xbf,0x68,0x6,0x6b,0xc,0x1a,0xff,0x1f,0x80,0x8,0xd4,0x27,0xf0,
  0xd,0x5a,0x5,0x74,0x4f,0xba,0xb8,0xe6,0x35,0xa8,0xfa,0x9d,0xf6,0x97,0xa2,0xe7,
  0x33,0x9a,0x51,0x27,0x3,0x77,0x25,0x61,0x85,0x57,0x35,0x2b,0xcc,0x2b,0xc0,0x63,
  0x50,0x12,0xd2,0x1c,0xfb,0x7e,0x82,0x8f,0x26,0xc9,0x12,0xf1,0x2f,0x93,0x80,0xb2,
  0xd3,0xd1,0x63,0xca,0x15,0xd,0x49,0xf8,0xc,0x43,0x50,0x46,0xbe,0xa3,0x9e,0x3f,
  0xb7,0xe6,0x38,0xa0,0x23,0xcd,0xc0,0x1f,0x15,0xfc,0xcb,0x35,0x2b,0xf8,0xf7,0x43,
  0xe6,0xdf,0xfd,0x5e,0x60,0x67,0xff,0xdc,0x60,0x79,0x6c,0xf9,0x3f,0x2e,0x70,0xd0,
  0xde,
    // /root/PhoneBox/PhoneScreen.qml
  0x0,0x0,0x5,0xe4,
  0x0,
  0x0,0x36,0xdd,0x78,0x9c,0xed,0x5b,0xed,0x8b,0x1c,0x35,0x18,0xff,0x7e,0x7f,0x45,
  0x9c,0xfb,0x52,0x5b,0xbb,0xdd,0xd7,0xf3,0x5c,0x38,0xe4,0xba,0x87,0x5a,0x2c,0x62,
  0x4f,0xe9,0x17,0xf1,0x43,0x76,0x26,0xb7,0x1b,0x6e,0x76,0xb2,0x64,0xb2,0x77,0x5e,
  0xed,0x81,0x20,0xb5,0xa0,0xd4,0x6b,0x95,0xfa,0x52,0x3c,0x8a,0xd8,0x82,0x1f,0x94,
  0x16,0x4,0x5f,0x50,0x11,0xfc,0x53,0x8a,0x7b,0xf4,0xbe,0xf9,0x27,0x98,0x64,0x26,
  0x73,0x3b,0x33,0x99,0x99,0xec,0x75,0xcf,0x2d,0x74,0x9e,0x85,0x30,0x49,0x9e,0x49,
  0x32,0x4f,0x7e,0xf9,0x3d,0x4f,0x26,0xb3,0x78,0x30,0x24,0x94,0x81,0x4b,0xec,0xd2,
  0x8,0xdb,0x9b,0xa0,0x5e,0xa9,0xd5,0x17,0x70,0xac,0xac,0xd2,0x21,0x1e,0xa3,0xc4,
  0xf5,0xb5,0x95,0x17,0xe1,0xe,0x19,0x31,0x1f,0xd4,0x44,0xdd,0xc2,0x3a,0xb2,0x19,
  0xf4,0x7a,0x2e,0x2,0xef,0x2f,0x0,0x2e,0x36,0x71,0x9,0x6d,0x3,0x6b,0xbb,0x8f,
  0x19,0xb2,0x64,0x91,0x4c,0x86,0x94,0xc,0x11,0x65,0x3b,0xc0,0x67,0x14,0x7b,0x3d,
  0x60,0x8f,0x28,0x45,0x1e,0x7b,0x8b,0x41,0x86,0xb8,0xba,0x83,0xa1,0xcb,0x8b,0x2d,
  0x70,0xee,0xdc,0x51,0xe6,0x5,0x60,0x61,0xcf,0x26,0x83,0xf0,0x1a,0xda,0xc,0x6f,
  0x85,0x6d,0x26,0x9b,0x1b,0xf6,0x89,0x87,0xde,0x18,0xd,0xba,0x48,0x74,0x5e,0x6b,
  0x35,0x97,0x97,0x5b,0x4b,0xcd,0x66,0xab,0xa1,0x57,0xb7,0xa1,0xeb,0xae,0x8d,0x28,
  0x64,0x98,0x78,0x5c,0xbf,0x5a,0x6d,0x57,0xeb,0x13,0x83,0x5d,0x27,0xdb,0xe1,0xe3,
  0x8,0x81,0x9e,0xdd,0x27,0xd4,0xaf,0x6c,0x60,0xd7,0x6d,0x83,0x21,0x14,0xe3,0x4e,
  0x55,0xe,0x20,0xed,0x61,0xcf,0x6f,0x83,0x7a,0x35,0xaa,0xf3,0x87,0xd0,0xe6,0xbd,
  0xc5,0xca,0xa2,0xb,0xfe,0xa0,0x17,0xd1,0x6,0x3,0x3e,0x76,0x10,0x38,0xb,0x82,
  0xb1,0x3,0x7,0xfb,0x43,0x17,0xee,0xf0,0x66,0x1d,0xb0,0x89,0x76,0x86,0xd0,0x89,
  0x6e,0x48,0x5a,0x5a,0xc9,0x36,0x76,0x58,0x5f,0x8d,0xab,0x22,0x73,0xe0,0x34,0xa8,
  0x56,0x96,0x62,0x5a,0x7d,0x84,0x7b,0x7d,0x16,0xa9,0x5,0xd9,0x98,0x86,0x9a,0x39,
  0x46,0xa1,0xe7,0x7,0x6a,0x56,0x4c,0x21,0x96,0xe9,0x10,0x77,0x34,0xf0,0x12,0x43,
  0x99,0x34,0x8,0x4f,0xf0,0x15,0x8e,0x23,0xe8,0x76,0x78,0x4b,0x62,0x5a,0x54,0xd7,
  0x89,0x8a,0x54,0xb,0x3a,0xb3,0x69,0xc7,0x10,0x9a,0xf1,0x4d,0x31,0xf7,0xc0,0x8b,
  0x19,0x30,0xa5,0x97,0x65,0xbd,0x84,0x15,0x9b,0xd5,0x74,0x9f,0x42,0x94,0xf9,0x96,
  0xf4,0xd5,0x85,0xb6,0xcb,0x1c,0xbf,0x1c,0x5b,0xc,0x6e,0x49,0x51,0x6,0xb5,0xa5,
  0xb5,0x2e,0x78,0x29,0x8,0x26,0x25,0xb2,0x5f,0x4d,0x3f,0xd8,0xcc,0x71,0x8,0x79,
  0x1b,0xbd,0xc7,0x72,0x6,0x23,0x84,0x71,0x95,0xf6,0xe4,0x8a,0xcb,0xd5,0xde,0xe0,
  0x53,0x5d,0x19,0x12,0xcc,0xd7,0x3a,0xbe,0xc2,0x97,0x7a,0xbd,0x99,0xab,0x1e,0x5a,
  0x72,0x92,0x1e,0xc0,0xca,0xca,0x4a,0xb4,0xfa,0xc1,0xcb,0xc0,0x5a,0x6c,0x48,0xb1,
  0x0,0xb7,0xf8,0x62,0xb3,0xb3,0xfa,0x4a,0xab,0xaa,0xb7,0xb6,0x12,0x65,0xc2,0x2d,
  0xce,0x1,0xd8,0x4e,0x21,0x32,0x5e,0x9c,0xd9,0xd2,0xee,0xf4,0xd6,0x3c,0x3f,0x62,
  0x8c,0xe8,0x56,0xc9,0xa4,0x84,0xd0,0x6b,0x64,0xcf,0x96,0x10,0x85,0xc0,0x2,0xb5,
  0x60,0x72,0xac,0x47,0xdf,0xdc,0xce,0x37,0x49,0x17,0xda,0x9b,0x3d,0x4a,0x46,0x9e,
  0xd3,0x2e,0x5c,0x1b,0x93,0xa2,0x90,0xbe,0x68,0x4b,0xc9,0xef,0x44,0x8,0x85,0xe,
  0x1e,0x71,0x5e,0xac,0xb5,0x72,0x55,0xb3,0x8d,0x1b,0xf4,0xca,0x67,0xc6,0x63,0x17,
  0x18,0x1a,0xb4,0x4d,0x20,0x2a,0x24,0x84,0x69,0x30,0xc5,0x22,0x63,0xfc,0x6c,0x13,
  0xbe,0x2b,0x4f,0x8e,0x88,0x6c,0xd5,0xc5,0x3d,0x6f,0xc0,0xfb,0x9,0x6,0x57,0x91,
  0xf9,0xd7,0xa,0xe0,0xa4,0x44,0xa1,0x4f,0xdb,0xc8,0x65,0x83,0x46,0xb2,0x4d,0xa7,
  0xaf,0x49,0x97,0xa6,0x4b,0x74,0x3c,0xdb,0xe1,0x4e,0x93,0x3b,0x50,0xc8,0x46,0x3e,
  0x20,0x14,0xc0,0x2d,0xc8,0x60,0x7a,0x64,0x33,0x22,0xda,0x7a,0x46,0xbd,0x31,0xd3,
  0x3e,0xb9,0x1f,0xd2,0xda,0x41,0x88,0x80,0xe1,0x2c,0x9,0x3b,0x34,0x49,0x4d,0xe3,
  0xef,0x94,0x28,0xb3,0xe4,0xe9,0x64,0x56,0xf0,0xb9,0x5b,0x95,0x93,0xc5,0xc9,0x98,
  0xcf,0x9b,0x24,0x52,0x19,0x2,0x65,0xde,0x61,0xca,0x8,0x98,0xb3,0x47,0x6,0xe,
  0x26,0x65,0xb,0xfb,0xb8,0xeb,0x22,0x1d,0xad,0x47,0x1,0x1e,0xb8,0x7a,0x35,0x9b,
  0xf5,0x8d,0xd8,0xdd,0xd8,0xde,0x42,0xc,0x6c,0x2e,0xc4,0xc4,0xee,0x42,0x22,0x5a,
  0xac,0x56,0xcf,0x77,0xd6,0x9a,0xf9,0x3,0x56,0x94,0x98,0x11,0x4c,0x28,0xc9,0xad,
  0x34,0x24,0xc1,0x63,0xd9,0x46,0x48,0xe8,0x47,0xfe,0xbd,0x7b,0xeb,0x5e,0x31,0x13,
  0x4e,0x49,0x9c,0xc9,0x90,0xa0,0xb9,0x7c,0xc2,0xfc,0x26,0x44,0x5b,0x98,0x60,0xb5,
  0x4c,0x2f,0x91,0x19,0xed,0x2a,0x99,0xd,0xdb,0x4c,0xb6,0xd4,0x25,0x3c,0x74,0x18,
  0x44,0xf7,0x7,0xd9,0x39,0xc7,0x7c,0x79,0xcb,0x97,0xc7,0x65,0xe3,0x5b,0x7f,0x8c,
  0xaf,0xdd,0x17,0x61,0x59,0x21,0x4,0x40,0x7e,0x84,0x77,0xf8,0xc1,0x9d,0xc7,0xf,
  0xf6,0xff,0xf9,0xf5,0x47,0x19,0xe3,0xe5,0x23,0x6a,0xaa,0xa5,0x97,0x84,0x5e,0x2d,
  0x3f,0x1a,0x9d,0xdd,0xb4,0xa,0xd1,0xd3,0xe0,0x73,0xe2,0xd9,0xd5,0x96,0x77,0x96,
  0xc1,0xa7,0xf1,0xb4,0x5a,0x7c,0xaf,0x5c,0x9b,0xce,0x6e,0x5,0x74,0xf8,0x6c,0xd8,
  0xcd,0x0,0xc2,0x7,0x5f,0xfe,0x7c,0x78,0xfb,0x2f,0x60,0x81,0x33,0xb1,0x77,0xe,
  0x53,0xa0,0xda,0x6e,0xd6,0x50,0x3,0x4e,0x89,0xea,0xfa,0xdc,0x67,0xc7,0xc8,0x7b,
  0xcf,0x36,0x3e,0x7d,0x5d,0xbe,0x2f,0x1,0xa7,0xc2,0x1,0x1,0xec,0x81,0x10,0x1e,
  0x60,0x40,0x1c,0xf4,0x7c,0xea,0x9e,0x57,0x29,0x76,0x32,0x26,0x3a,0xe7,0xa1,0x72,
  0x21,0x37,0x1b,0xd3,0xda,0xd2,0xdd,0xf0,0x8,0xa1,0xa1,0xad,0x2e,0x22,0x7b,0xfd,
  0x4b,0x6,0x34,0x44,0xfc,0x19,0x68,0xe,0xb2,0x85,0x95,0xdc,0x36,0x78,0xc7,0xaa,
  0x89,0xd7,0x6d,0x75,0x91,0x34,0x44,0xd2,0x14,0x49,0x4b,0x24,0x4b,0x22,0x79,0x51,
  0x24,0xcb,0x22,0x79,0x49,0x24,0xa7,0x45,0x52,0x15,0xc9,0xa2,0xf5,0xee,0xc9,0x6e,
  0x91,0x97,0xcd,0x82,0xb5,0x82,0xb8,0x2a,0x58,0xbc,0xf2,0x61,0xd7,0x78,0xe8,0x7a,
  0xb2,0xdb,0xe4,0x8d,0x96,0xf8,0x15,0xc7,0x44,0x5d,0x42,0x1d,0x44,0x2b,0x53,0x6f,
  0xaf,0xc3,0xfb,0x54,0x38,0x6b,0xbc,0x1d,0x7f,0xfa,0x77,0xe3,0x29,0x52,0xcb,0x8f,
  0x12,0x85,0x3c,0x93,0x3b,0x72,0x7,0x53,0xe,0x4c,0xe1,0x54,0x20,0xa5,0x64,0xdb,
  0x7,0xa7,0xc4,0x2e,0x4f,0x5,0x66,0xd2,0xed,0xf8,0x69,0xee,0xcb,0x7e,0xe7,0x68,
  0xb2,0x59,0x3b,0x41,0xee,0x8b,0xc8,0xad,0x39,0x5,0xb9,0x15,0x80,0x30,0xc,0x73,
  0xfe,0xfe,0x45,0xfc,0xc0,0xa3,0x8f,0x3e,0x3,0x41,0xa8,0xca,0x2f,0x6f,0x82,0xa0,
  0x34,0x7b,0xa5,0x19,0xc7,0x97,0x86,0xb1,0x65,0xd1,0x64,0x1f,0xe5,0x8e,0xae,0xa2,
  0xb,0x3e,0xe9,0xeb,0x82,0xe4,0xd4,0xb1,0x41,0x78,0x52,0x3,0xba,0x92,0x45,0xfd,
  0x48,0x6f,0xda,0xc3,0x82,0x46,0x9c,0xf,0x66,0x7d,0x5a,0x90,0xe1,0x69,0x8d,0x77,
  0xa7,0x91,0x43,0x4c,0x7,0x36,0x47,0xce,0x30,0x4d,0x69,0xba,0x35,0x73,0x59,0x34,
  0x85,0x24,0x87,0x89,0x23,0xae,0x94,0x4a,0xae,0x3f,0x2a,0x78,0x6b,0x50,0xe0,0x80,
  0x42,0x18,0x1e,0xde,0xfd,0xe9,0xf0,0xfa,0xde,0x59,0x3d,0x90,0xa6,0x75,0x38,0x86,
  0x5b,0xef,0xa4,0x7b,0x29,0x7a,0x6b,0x1e,0x77,0x2b,0xd9,0xe1,0x64,0xbe,0x3b,0xd1,
  0xb3,0xdd,0x34,0xee,0xc3,0xdc,0x6d,0x18,0x3f,0xda,0x13,0x3a,0x89,0x63,0x3b,0x87,
  0x63,0x91,0xfc,0xff,0x85,0xc6,0x33,0x25,0x1a,0x4b,0x34,0x26,0x4b,0x74,0xf4,0xb9,
  0x2e,0x63,0x8a,0x39,0x92,0xe7,0xe7,0x1f,0x8e,0xbf,0x7b,0x58,0x92,0x67,0x9,0xd7,
  0xa7,0x82,0x3c,0x25,0x1a,0x4b,0xf2,0x2c,0xd1,0x68,0xbe,0x5f,0x9b,0x27,0x79,0xee,
  0x7f,0x7d,0xf8,0xfb,0x57,0x25,0x5a,0x4b,0xb4,0x26,0x4b,0xe6,0x0,0xc6,0xf1,0xbd,
  0x3b,0x7,0x5f,0xfc,0x16,0xbc,0x26,0x2f,0x21,0x59,0x42,0x32,0x59,0x32,0xf,0x7e,
  0xbc,0x7e,0xe3,0xe0,0x93,0xef,0x4b,0x30,0x96,0x60,0x4c,0x96,0xcc,0x1,0x8c,0x8f,
  0xff,0xfc,0xe1,0xe0,0xd3,0xfb,0x25,0x18,0x4b,0x30,0x26,0x4b,0xe6,0xe1,0xac,0xaf,
  0xdd,0x38,0xd8,0xbb,0x59,0x82,0xb1,0x4,0x63,0xb2,0x64,0x1e,0xcc,0xf8,0x60,0x7f,
  0xfc,0xf1,0xb7,0xe3,0xbd,0x87,0x25,0x1e,0x4b,0x3c,0xea,0x73,0xc1,0x55,0x90,0xca,
  0x84,0xef,0xbe,0x83,0x83,0x4c,0xbb,0xcf,0xf1,0x80,0xd4,0x81,0x99,0xfc,0x22,0xd6,
  0x41,0x3,0x22,0x95,0xf4,0xff,0xfb,0xc9,0xff,0x12,0x4f,0x69,0xd1,0xd8,0x89,0x19,
  0x8d,0x1d,0x98,0x19,0xfd,0x49,0x68,0xe2,0x63,0x8e,0xe8,0x42,0xbb,0x96,0xc2,0x55,
  0xc0,0x23,0xd5,0xf1,0x5e,0xe2,0xdc,0x92,0x78,0x1d,0x17,0xdb,0x9b,0xc8,0x49,0x9e,
  0xde,0x6a,0x3e,0x5b,0xd9,0x35,0xea,0x25,0xfc,0xa6,0xcf,0xb4,0x97,0xf4,0x9,0xb1,
  0x59,0x37,0xba,0x6d,0x60,0x4e,0x37,0xc9,0xf,0x8b,0xd4,0x74,0xef,0xfe,0x7,0x13,
  0xf3,0x29,0xb8,
    // /root/PhoneBox/StatusScreen.qml
  0x0,0x0,0x5,0x6,
  0x0,
  0x0,0x1f,0x6b,0x78,0x9c,0xe5,0x59,0x5b,0x6f,0x1b,0x45,0x14,0x7e,0xcf,0xaf,0x18,
  0x19,0xf1,0x2,0x89,0xb3,0xbb,0x71,0x42,0x6a,0x11,0x50,0x9b,0xa,0x8a,0x40,0x55,
  0x9b,0x44,0xe2,0x1,0xf1,0x30,0xde,0x1d,0xdb,0xa3,0xae,0x77,0xac,0xdd,0x71,0x9c,
  0x10,0x45,0xa,0x2f,0x5,0x95,0x96,0xa6,0x4a,0x90,0x50,0x2b,0x88,0x22,0x40,0x5,
  0x55,0x50,0xf1,0x80,0x5a,0x51,0x85,0xfe,0x97,0x2a,0xdb,0x38,0x6f,0xfd,0x9,0xcc,
  0xec,0xcd,0xde,0x9d,0xbd,0x4c,0x5c,0xc7,0x21,0xe2,0x8b,0x94,0xd8,0x67,0x6e,0xdf,
  0x9c,0x73,0xe6,0x9c,0x39,0x13,0xdc,0x6a,0x13,0x9b,0x82,0xeb,0xf4,0x7a,0x7,0xeb,
  0x37,0x80,0x56,0x56,0xb5,0x9,0x1c,0x93,0x95,0x17,0x89,0x45,0x6d,0x62,0x3a,0xa9,
  0x8d,0x9f,0xc0,0x75,0xd2,0xa1,0xe,0x50,0x79,0xdb,0xc4,0x12,0xd2,0x29,0xb4,0x1a,
  0x26,0x2,0x1b,0x13,0x80,0x41,0x27,0x26,0xb1,0xab,0xa0,0xd4,0x6d,0x62,0x8a,0x4a,
  0x9e,0xc8,0xfb,0xb5,0x48,0xcc,0x4e,0xcb,0xa,0x3a,0x71,0x40,0x4b,0x6f,0x12,0xdb,
  0x29,0xd7,0xb1,0x69,0x56,0x41,0x1b,0xda,0xc8,0xa2,0x42,0x63,0xb,0xda,0xd,0x6c,
  0x39,0x55,0xa0,0x29,0x51,0x9b,0xd3,0x86,0x3a,0xb6,0x1a,0x31,0x59,0xf4,0x61,0x7a,
  0x1a,0x5c,0x41,0xd0,0x40,0x76,0x24,0x59,0x22,0xdd,0x81,0x55,0x39,0xba,0xd8,0xa0,
  0xcd,0x70,0xc9,0xb2,0xf7,0x2d,0xd6,0x9e,0xb6,0x40,0x6c,0x11,0x8e,0x15,0xb4,0x46,
  0x13,0xf3,0x72,0x50,0x26,0x66,0xbb,0x3f,0xde,0xf9,0xa7,0xf7,0xe4,0x71,0x49,0x68,
  0xae,0x33,0xc5,0x96,0xdb,0x4,0x5b,0x74,0x19,0x7f,0x81,0xaa,0x40,0xad,0xa4,0x77,
  0xa9,0x11,0xd3,0xa8,0x2,0x6a,0x77,0x90,0xd0,0x1e,0xb0,0x9f,0x8f,0x73,0xdb,0x1c,
  0x96,0x69,0xef,0xf9,0x76,0x6f,0xfb,0x17,0xf7,0xbb,0x9b,0xa7,0x49,0x56,0xd5,0x46,
  0xc4,0xf6,0xf0,0xf9,0xbe,0x7b,0xf7,0xc9,0x79,0xd0,0xeb,0xd1,0x9f,0xcf,0xdc,0xaf,
  0x1f,0x9d,0x7,0xa6,0x87,0x4f,0x6f,0x1d,0x3e,0xfd,0xa6,0xb7,0x7f,0xfb,0x78,0x6b,
  0xef,0xe8,0xdb,0xaf,0x4e,0x93,0xb2,0xa6,0x64,0x71,0xee,0x7f,0xea,0x1f,0xdc,0x44,
  0x64,0x49,0xcc,0x95,0x79,0x7c,0x9b,0x8,0x37,0x9a,0x6c,0x63,0x6a,0x4c,0x1a,0xc6,
  0xa5,0x37,0x74,0xf,0xa5,0xbc,0x95,0x59,0x10,0x59,0xa6,0x90,0x76,0x1c,0x60,0x62,
  0xa7,0x1f,0x94,0x84,0x10,0x26,0xc3,0x26,0xa,0x26,0xea,0x6c,0xb6,0x81,0x96,0x50,
  0x1b,0x41,0x8a,0xec,0x14,0x23,0xb5,0x88,0x81,0x58,0x7c,0xfc,0x4c,0x68,0xe0,0xd8,
  0x70,0x4c,0xc2,0x4d,0xe8,0xde,0xd9,0x7f,0xf9,0xf0,0x40,0x2d,0x4d,0x2,0x1d,0xda,
  0x36,0x46,0xb6,0x67,0xd6,0xdf,0xdd,0x7,0x7,0xbd,0x2f,0x77,0x8f,0xb7,0xee,0xb3,
  0x6,0x7,0x37,0x2c,0xc8,0x26,0xaa,0x4c,0x2,0xba,0xde,0x66,0x56,0x2c,0x55,0x3e,
  0x64,0xe2,0x4e,0xdb,0x24,0x90,0x59,0xad,0xa4,0xcd,0xce,0x95,0x95,0x1a,0x93,0x18,
  0xa4,0x6b,0x5,0x32,0xb5,0x3c,0xff,0x31,0x17,0x21,0xb,0xd6,0x4c,0x14,0xd8,0x76,
  0x73,0x52,0x82,0x8a,0x96,0x42,0xe5,0xe8,0xe1,0x33,0xf7,0xd6,0xaf,0x12,0x54,0x14,
  0x81,0xc8,0x8c,0x16,0xc8,0x4e,0xce,0x64,0x26,0x8d,0xc9,0xee,0x5f,0x2c,0x90,0xc,
  0xc5,0x64,0x78,0x95,0x54,0x5e,0xc7,0x3a,0xa3,0x24,0x32,0x2b,0x63,0x9b,0x99,0x71,
  0xd8,0x66,0x2e,0xc6,0xe4,0xc5,0xd6,0xee,0x0,0x1,0x25,0x22,0xe0,0xcb,0x73,0x18,
  0x24,0xd7,0xaf,0x43,0xd3,0x91,0x23,0xf0,0xce,0x59,0x13,0x98,0x8f,0x11,0x70,0x7f,
  0xfe,0x8d,0xb9,0x83,0x7b,0xe7,0xd1,0xcb,0xef,0xf7,0xc6,0xc7,0xe4,0xe8,0xe0,0x9e,
  0x7b,0xf7,0x27,0x21,0x78,0x30,0xe,0x2,0x13,0x6d,0x78,0x26,0x32,0x4e,0xf1,0xe0,
  0x6f,0xf7,0x8f,0xfb,0xec,0x70,0xf6,0x1e,0xff,0x10,0x63,0x73,0x6d,0x79,0xe5,0xea,
  0x0,0x8b,0x29,0x35,0x8b,0x86,0xff,0x75,0x80,0x85,0x2f,0x38,0x77,0x24,0x7c,0x93,
  0x68,0x39,0xde,0xf9,0x9a,0xcb,0xb,0xab,0x7f,0x2e,0x48,0x4,0x41,0x56,0x3e,0xe,
  0x51,0x94,0x9,0x43,0x84,0xf9,0xb9,0xa2,0xa4,0x36,0x7,0x89,0x1a,0x5b,0x6,0x5a,
  0x3,0x6f,0x2,0xd,0x2c,0x2c,0x2c,0x0,0x5,0xbc,0xcf,0x52,0x77,0xfd,0x2,0xff,
  0x29,0x81,0x78,0x79,0x51,0x48,0xdc,0x23,0x2f,0x54,0x1,0x83,0x8,0xcb,0x8d,0x55,
  0x64,0x53,0xac,0x43,0x73,0x91,0xed,0x80,0x2b,0x3d,0xd8,0x4b,0x5c,0x9c,0x39,0x4b,
  0x56,0xdd,0x50,0x48,0x8e,0x83,0xdd,0x39,0x56,0x48,0x83,0x6b,0xd7,0xe9,0x62,0xaa,
  0xa7,0xab,0x8e,0x63,0xd9,0x6b,0xce,0xd9,0xc,0x87,0xde,0x44,0xfa,0xd,0x6e,0x6d,
  0xef,0xfa,0x70,0x19,0x52,0x58,0xe,0x1c,0x20,0x77,0xd8,0x68,0xf4,0x90,0xbb,0x4f,
  0xe,0x66,0x5b,0x36,0xd,0xe5,0x66,0x2e,0x72,0xaa,0xd8,0xb0,0x56,0xdb,0xc4,0x3a,
  0xa6,0x9f,0xfa,0x8e,0x96,0xe1,0x3f,0x69,0x23,0xae,0x4,0x2e,0x97,0x63,0x98,0x10,
  0x6b,0xd1,0x6e,0x4d,0x54,0xa7,0xd7,0xa0,0x61,0x30,0x93,0x16,0x8e,0x5a,0x8f,0x46,
  0xf9,0xde,0xd,0xa6,0x99,0xe3,0x4e,0x81,0xfe,0x97,0xc2,0x19,0x6c,0x68,0xe0,0xe,
  0x2b,0x74,0xd5,0x62,0x8a,0xc1,0x9,0x9,0x16,0xc,0x4c,0xed,0x1d,0x10,0x45,0xb9,
  0xb4,0x78,0xb9,0xe2,0x1d,0x10,0xe1,0x9e,0x9b,0x85,0xc2,0xe,0x27,0x31,0x11,0x47,
  0x5f,0x83,0x2,0xc3,0xc1,0xd8,0xc0,0xd4,0xe3,0xff,0x65,0x9,0x4e,0x6a,0xde,0xb0,
  0x96,0x9c,0x97,0xea,0x1d,0x55,0x1,0x72,0xdd,0x43,0xf5,0x5f,0x90,0xea,0x3d,0xba,
  0x73,0x12,0x22,0xe5,0xd9,0xa4,0x8,0x52,0x9d,0x2e,0xa1,0x26,0x5c,0xc5,0xc4,0x6,
  0xc4,0x2,0x6b,0x92,0x6,0xe4,0xb8,0xda,0x69,0xd5,0x90,0x7d,0xd1,0xc2,0x2d,0x48,
  0x31,0x1b,0xbb,0x1,0x8c,0x8e,0xed,0x7d,0xf4,0xaa,0x38,0x20,0x66,0x90,0x34,0x14,
  0xf7,0xca,0xef,0x91,0xdd,0x9a,0xdd,0x92,0xd9,0x90,0x51,0x5,0xf,0xc2,0xaf,0x88,
  0xfb,0x31,0x93,0xa7,0xe3,0xdc,0x1,0x42,0x69,0x9c,0x7f,0xd0,0xd3,0x2b,0xf7,0x24,
  0x46,0xe3,0x5d,0xe3,0xd1,0x50,0x70,0x3f,0x39,0x5,0x25,0x25,0xdf,0x8c,0x92,0x38,
  0x33,0x2d,0xf1,0xd7,0x1,0xef,0x16,0x6,0x1c,0xca,0x96,0x6a,0x64,0x5c,0x73,0x38,
  0x64,0x43,0xa7,0x9c,0x5f,0x34,0xe5,0xb2,0x58,0x18,0x4a,0xa8,0xd,0x2d,0xc7,0xd7,
  0x46,0x7e,0x40,0x19,0x53,0xd6,0xcf,0xbf,0x82,0x25,0xc9,0xe8,0xde,0x6a,0x1f,0x59,
  0xc2,0xd3,0x70,0x16,0xfa,0xb7,0xaf,0x51,0xa4,0xbc,0xcc,0xf7,0x98,0x34,0x4,0x6f,
  0x34,0x3,0x71,0xc3,0xf7,0x8f,0xf7,0xbc,0x7b,0x6b,0x45,0x3a,0xc3,0x49,0x75,0x3a,
  0x69,0x3a,0xe6,0x8,0xfd,0x4b,0x7a,0x40,0xe8,0x6a,0x73,0xe0,0xed,0xe0,0x26,0xfe,
  0x16,0x98,0x91,0x1e,0x1d,0xbb,0xc2,0xbf,0x2b,0xea,0x65,0xd8,0xab,0x4a,0x88,0xd0,
  0x47,0x6a,0x84,0x52,0xd2,0x8a,0x1c,0xd5,0xff,0x3a,0x96,0xc4,0xc4,0x51,0xd8,0x81,
  0x7,0x8a,0x36,0xd2,0x31,0xdb,0x31,0xd6,0x89,0xe5,0xb0,0x40,0x68,0x3,0x5e,0x46,
  0xb2,0xd,0x18,0xc0,0x61,0xe,0x66,0x9a,0x2c,0xdb,0x17,0xce,0x23,0x11,0x96,0x43,
  0xac,0x62,0x7,0xb3,0x5b,0x7e,0x8a,0x2f,0xf2,0x2a,0x6a,0x4a,0x95,0x9a,0x24,0x23,
  0xc6,0x7b,0x53,0xf8,0x65,0x30,0xb7,0xe0,0xab,0xbd,0x9d,0x1f,0x3d,0xf3,0xbd,0xda,
  0xbb,0xbd,0x2d,0x67,0x3b,0x21,0x11,0xcc,0x9d,0xc1,0x6d,0xeb,0xbf,0x7d,0xe3,0xe0,
  0x35,0xfd,0xff,0xe8,0xc6,0x51,0x9c,0x13,0xfa,0xef,0xe7,0xe3,0xd8,0x42,0x2e,0x59,
  0xe,0xc9,0xb3,0x18,0xfc,0x6b,0xe5,0xc5,0xcd,0x7b,0xa0,0xc4,0x42,0x68,0xdf,0xbe,
  0xfe,0x33,0x4d,0xe1,0x78,0xc1,0xca,0xd2,0x55,0x61,0x14,0x59,0x87,0x74,0x73,0x8e,
  0x91,0x6a,0x60,0x27,0xa1,0x81,0xf0,0x65,0xea,0x54,0x75,0x50,0x59,0xbc,0xf8,0xc1,
  0xac,0x32,0xac,0xe,0xd2,0x5b,0x44,0x69,0x5c,0x92,0xfc,0x67,0xd6,0xe6,0xc4,0xe6,
  0xbf,0x67,0x6,0x46,0x47,
    // /root/PhoneBox/main.qml
  0x0,0x0,0x0,0x54,
  0x69,
  0x6d,0x70,0x6f,0x72,0x74,0x20,0x51,0x74,0x51,0x75,0x69,0x63,0x6b,0x20,0x32,0x2e,
  0x31,0x32,0xa,0x69,0x6d,0x70,0x6f,0x72,0x74,0x20,0x51,0x74,0x51,0x75,0x69,0x63,
  0x6b,0x2e,0x57,0x69,0x6e,0x64,0x6f,0x77,0x20,0x32,0x2e,0x31,0x32,0xa,0xa,0x50,
  0x68,0x6f,0x6e,0x65,0x42,0x6f,0x78,0x41,0x70,0x70,0x20,0x7b,0xa,0x20,0x20,0x20,
  0x20,0x69,0x64,0x3a,0x20,0x70,0x68,0x6f,0x6e,0x65,0x42,0x6f,0x78,0x41,0x70,0x70,
  0xa,0x7d,0xa,
  
};

static const unsigned char qt_resource_name[] = {
  // ConferenceScreen.qml
  0x0,0x14,
  0xb,0xc7,0x6d,0x1c,
  0x0,0x43,
  0x0,0x6f,0x0,0x6e,0x0,0x66,0x0,0x65,0x0,0x72,0x0,0x65,0x0,0x6e,0x0,0x63,0x0,0x65,0x0,0x53,0x0,0x63,0x0,0x72,0x0,0x65,0x0,0x65,0x0,0x6e,0x0,0x2e,
  0x0,0x71,0x0,0x6d,0x0,0x6c,
    // VideoScreen.qml
  0x0,0xf,
  0xe,0x49,0x12,0xdc,
  0x0,0x56,
  0x0,0x69,0x0,0x64,0x0,0x65,0x0,0x6f,0x0,0x53,0x0,0x63,0x0,0x72,0x0,0x65,0x0,0x65,0x0,0x6e,0x0,0x2e,0x0,0x71,0x0,0x6d,0x0,0x6c,
    // PhoneBoxApp.qml
  0x0,0xf,
  0x4,0xe1,0x22,0x7c,
  0x0,0x50,
  0x0,0x68,0x0,0x6f,0x0,0x6e,0x0,0x65,0x0,0x42,0x0,0x6f,0x0,0x78,0x0,0x41,0x0,0x70,0x0,0x70,0x0,0x2e,0x0,0x71,0x0,0x6d,0x0,0x6c,
    // SettingsScreen.qml
  0x0,0x12,
  0x4,0x22,0x91,0x1c,
  0x0,0x53,
  0x0,0x65,0x0,0x74,0x0,0x74,0x0,0x69,0x0,0x6e,0x0,0x67,0x0,0x73,0x0,0x53,0x0,0x63,0x0,0x72,0x0,0x65,0x0,0x65,0x0,0x6e,0x0,0x2e,0x0,0x71,0x0,0x6d,
  0x0,0x6c,
    // PhoneScreen.qml
  0x0,0xf,
  0x7,0x55,0x3a,0xfc,
  0x0,0x50,
  0x0,0x68,0x0,0x6f,0x0,0x6e,0x0,0x65,0x0,0x53,0x0,0x63,0x0,0x72,0x0,0x65,0x0,0x65,0x0,0x6e,0x0,0x2e,0x0,0x71,0x0,0x6d,0x0,0x6c,
    // StatusScreen.qml
  0x0,0x10,
  0xc,0x46,0xcb,0x1c,
  0x0,0x53,
  0x0,0x74,0x0,0x61,0x0,0x74,0x0,0x75,0x0,0x73,0x0,0x53,0x0,0x63,0x0,0x72,0x0,0x65,0x0,0x65,0x0,0x6e,0x0,0x2e,0x0,0x71,0x0,0x6d,0x0,0x6c,
    // main.qml
  0x0,0x8,
  0x8,0x1,0x5a,0x5c,
  0x0,0x6d,
  0x0,0x61,0x0,0x69,0x0,0x6e,0x0,0x2e,0x0,0x71,0x0,0x6d,0x0,0x6c,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x7,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/SettingsScreen.qml
  0x0,0x0,0x0,0x76,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0xd,0xa2,
0x0,0x0,0x1,0x97,0xa0,0x38,0x5f,0xfc,
  // :/PhoneBoxApp.qml
  0x0,0x0,0x0,0x52,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x5,0xfe,
0x0,0x0,0x1,0x97,0xa0,0x35,0x35,0x20,
  // :/PhoneScreen.qml
  0x0,0x0,0x0,0xa0,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x14,0x28,
0x0,0x0,0x1,0x97,0xa0,0x36,0x7f,0x7e,
  // :/main.qml
  0x0,0x0,0x0,0xea,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x1f,0x1a,
0x0,0x0,0x1,0x97,0xa0,0x38,0x7e,0x38,
  // :/ConferenceScreen.qml
  0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0xa0,0x37,0xb,0x2f,
  // :/StatusScreen.qml
  0x0,0x0,0x0,0xc4,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x1a,0x10,
0x0,0x0,0x1,0x97,0xa0,0x37,0x93,0xcf,
  // :/VideoScreen.qml
  0x0,0x0,0x0,0x2e,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x3,0x9b,
0x0,0x0,0x1,0x97,0xa0,0x35,0xcd,0x7d,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_qml)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qml)(); }
   } dummy;
}
