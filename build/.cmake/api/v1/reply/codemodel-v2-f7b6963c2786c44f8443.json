{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.5"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "PhoneBox", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "PhoneBox::@6890427a1f51a3e7e1df", "jsonFile": "target-PhoneBox-Debug-3f6e2e5bc6fca7981edb.json", "name": "PhoneBox", "projectIndex": 0}, {"directoryIndex": 0, "id": "PhoneBox_autogen::@6890427a1f51a3e7e1df", "jsonFile": "target-PhoneBox_autogen-Debug-83adde2e33432b3583bc.json", "name": "PhoneBox_autogen", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/root/PhoneBox/build", "source": "/root/PhoneBox"}, "version": {"major": 2, "minor": 0}}