{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/CMakeFiles/3.16.3/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isGenerated": true, "path": "build/CMakeFiles/3.16.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/Internal/CMakeCheckCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/Platform/Linux-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5/Qt5Config.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/usr/share/cmake-3.16/Modules/CMakeParseArguments.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Quick/Qt5QuickConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Quick/Qt5QuickConfig.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5QmlModels/Qt5QmlModelsConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5QmlModels/Qt5QmlModelsConfig.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5QmlConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5QmlConfig.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5NetworkConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5NetworkConfig.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5Network_QConnmanEnginePlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5Network_QGenericEnginePlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5Network_QNetworkManagerEnginePlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5QmlConfigExtras.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QDebugMessageServiceFactory.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QLocalClientConnectionFactory.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebugServerFactory.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebuggerServiceFactory.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlInspectorServiceFactory.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugConnectorFactory.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugServiceFactory.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlPreviewServiceFactory.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlProfilerServiceFactory.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQuickProfilerAdapterFactory.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QTcpServerConnectionFactory.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QComposePlatformInputContextPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEglFSEmulatorIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEglFSIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEglFSKmsEglDeviceIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEglFSX11IntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEvdevKeyboardPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEvdevMousePlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEvdevTabletPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEvdevTouchScreenPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QGtk3ThemePlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QIbusPlatformInputContextPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QLinuxFbIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalEglIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QVncIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWaylandEglPlatformIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWaylandIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWaylandXCompositeEglPlatformIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWaylandXCompositeGlxPlatformIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QXcbEglIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QXcbGlxIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QXcbIntegrationPlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake"}, {"isExternal": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake"}, {"path": "qml.qrc"}], "kind": "cmakeFiles", "paths": {"build": "/root/PhoneBox/build", "source": "/root/PhoneBox"}, "version": {"major": 1, "minor": 0}}