{"artifacts": [{"path": "PhoneBox"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "target_compile_definitions"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 35, "parent": 0}, {"command": 1, "file": 0, "line": 43, "parent": 0}, {"command": 2, "file": 0, "line": 41, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g  "}, {"backtrace": 2, "fragment": "-fPIC"}, {"fragment": "-std=gnu++11"}], "defines": [{"backtrace": 2, "define": "QT_CORE_LIB"}, {"backtrace": 2, "define": "QT_GUI_LIB"}, {"backtrace": 2, "define": "QT_NETWORK_LIB"}, {"backtrace": 2, "define": "QT_QMLMODELS_LIB"}, {"backtrace": 3, "define": "QT_QML_DEBUG"}, {"backtrace": 2, "define": "QT_QML_LIB"}, {"backtrace": 2, "define": "QT_QUICK_LIB"}], "includes": [{"path": "/root/PhoneBox/build"}, {"path": "/root/PhoneBox"}, {"backtrace": 0, "path": "/root/PhoneBox/build/PhoneBox_autogen/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/include"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork"}, {"backtrace": 2, "isSystem": true, "path": "/opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 9]}], "dependencies": [{"backtrace": 0, "id": "PhoneBox_autogen::@6890427a1f51a3e7e1df"}], "id": "PhoneBox::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/Qt5.14.2/5.14.2/gcc_64/lib", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Quick.so.5.14.2", "role": "libraries"}, {"fragment": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5QmlModels.so.5.14.2", "role": "libraries"}, {"fragment": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Qml.so.5.14.2", "role": "libraries"}, {"fragment": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Network.so.5.14.2", "role": "libraries"}, {"fragment": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Gui.so.5.14.2", "role": "libraries"}, {"backtrace": 2, "fragment": "/opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Core.so.5.14.2", "role": "libraries"}], "language": "CXX"}, "name": "PhoneBox", "nameOnDisk": "PhoneBox", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 9]}, {"name": "", "sourceIndexes": [8]}, {"name": "CMake Rules", "sourceIndexes": [10]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/PhoneBox_autogen/mocs_compilation.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "landevicemodel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "optimized_video_modifier.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "videoDetect.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "video_generator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "video_modifier.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "video_normalizer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "qml.qrc", "sourceGroupIndex": 1}, {"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/PhoneBox_autogen/EWIEGA46WW/qrc_qml.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/PhoneBox_autogen/EWIEGA46WW/qrc_qml.cpp.rule", "sourceGroupIndex": 2}], "type": "EXECUTABLE"}