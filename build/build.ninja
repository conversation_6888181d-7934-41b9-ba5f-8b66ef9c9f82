# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.16

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: PhoneBox
# Configuration: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include rules.ninja


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /root/PhoneBox/build && /usr/bin/cmake -S/root/PhoneBox -B/root/PhoneBox/build
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /root/PhoneBox/build && /usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util

# =============================================================================
# Object build statements for EXECUTABLE target PhoneBox


#############################################
# Order-only phony target for PhoneBox

build cmake_object_order_depends_target_PhoneBox: phony || PhoneBox_autogen PhoneBox_autogen/EWIEGA46WW/qrc_qml.cpp

build CMakeFiles/PhoneBox.dir/PhoneBox_autogen/mocs_compilation.cpp.o: CXX_COMPILER__PhoneBox PhoneBox_autogen/mocs_compilation.cpp || cmake_object_order_depends_target_PhoneBox
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB
  DEP_FILE = CMakeFiles/PhoneBox.dir/PhoneBox_autogen/mocs_compilation.cpp.o.d
  FLAGS = -g   -fPIC -std=gnu++11
  INCLUDES = -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui
  OBJECT_DIR = CMakeFiles/PhoneBox.dir
  OBJECT_FILE_DIR = CMakeFiles/PhoneBox.dir/PhoneBox_autogen

build CMakeFiles/PhoneBox.dir/landevicemodel.cpp.o: CXX_COMPILER__PhoneBox ../landevicemodel.cpp || cmake_object_order_depends_target_PhoneBox
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB
  DEP_FILE = CMakeFiles/PhoneBox.dir/landevicemodel.cpp.o.d
  FLAGS = -g   -fPIC -std=gnu++11
  INCLUDES = -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui
  OBJECT_DIR = CMakeFiles/PhoneBox.dir
  OBJECT_FILE_DIR = CMakeFiles/PhoneBox.dir

build CMakeFiles/PhoneBox.dir/main.cpp.o: CXX_COMPILER__PhoneBox ../main.cpp || cmake_object_order_depends_target_PhoneBox
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB
  DEP_FILE = CMakeFiles/PhoneBox.dir/main.cpp.o.d
  FLAGS = -g   -fPIC -std=gnu++11
  INCLUDES = -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui
  OBJECT_DIR = CMakeFiles/PhoneBox.dir
  OBJECT_FILE_DIR = CMakeFiles/PhoneBox.dir

build CMakeFiles/PhoneBox.dir/optimized_video_modifier.cpp.o: CXX_COMPILER__PhoneBox ../optimized_video_modifier.cpp || cmake_object_order_depends_target_PhoneBox
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB
  DEP_FILE = CMakeFiles/PhoneBox.dir/optimized_video_modifier.cpp.o.d
  FLAGS = -g   -fPIC -std=gnu++11
  INCLUDES = -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui
  OBJECT_DIR = CMakeFiles/PhoneBox.dir
  OBJECT_FILE_DIR = CMakeFiles/PhoneBox.dir

build CMakeFiles/PhoneBox.dir/videoDetect.cpp.o: CXX_COMPILER__PhoneBox ../videoDetect.cpp || cmake_object_order_depends_target_PhoneBox
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB
  DEP_FILE = CMakeFiles/PhoneBox.dir/videoDetect.cpp.o.d
  FLAGS = -g   -fPIC -std=gnu++11
  INCLUDES = -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui
  OBJECT_DIR = CMakeFiles/PhoneBox.dir
  OBJECT_FILE_DIR = CMakeFiles/PhoneBox.dir

build CMakeFiles/PhoneBox.dir/video_generator.cpp.o: CXX_COMPILER__PhoneBox ../video_generator.cpp || cmake_object_order_depends_target_PhoneBox
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB
  DEP_FILE = CMakeFiles/PhoneBox.dir/video_generator.cpp.o.d
  FLAGS = -g   -fPIC -std=gnu++11
  INCLUDES = -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui
  OBJECT_DIR = CMakeFiles/PhoneBox.dir
  OBJECT_FILE_DIR = CMakeFiles/PhoneBox.dir

build CMakeFiles/PhoneBox.dir/video_modifier.cpp.o: CXX_COMPILER__PhoneBox ../video_modifier.cpp || cmake_object_order_depends_target_PhoneBox
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB
  DEP_FILE = CMakeFiles/PhoneBox.dir/video_modifier.cpp.o.d
  FLAGS = -g   -fPIC -std=gnu++11
  INCLUDES = -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui
  OBJECT_DIR = CMakeFiles/PhoneBox.dir
  OBJECT_FILE_DIR = CMakeFiles/PhoneBox.dir

build CMakeFiles/PhoneBox.dir/video_normalizer.cpp.o: CXX_COMPILER__PhoneBox ../video_normalizer.cpp || cmake_object_order_depends_target_PhoneBox
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB
  DEP_FILE = CMakeFiles/PhoneBox.dir/video_normalizer.cpp.o.d
  FLAGS = -g   -fPIC -std=gnu++11
  INCLUDES = -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui
  OBJECT_DIR = CMakeFiles/PhoneBox.dir
  OBJECT_FILE_DIR = CMakeFiles/PhoneBox.dir

build CMakeFiles/PhoneBox.dir/PhoneBox_autogen/EWIEGA46WW/qrc_qml.cpp.o: CXX_COMPILER__PhoneBox PhoneBox_autogen/EWIEGA46WW/qrc_qml.cpp || cmake_object_order_depends_target_PhoneBox
  DEFINES = -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_QMLMODELS_LIB -DQT_QML_DEBUG -DQT_QML_LIB -DQT_QUICK_LIB
  DEP_FILE = CMakeFiles/PhoneBox.dir/PhoneBox_autogen/EWIEGA46WW/qrc_qml.cpp.o.d
  FLAGS = -g   -fPIC -std=gnu++11
  INCLUDES = -I. -I../ -IPhoneBox_autogen/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtCore -isystem /opt/Qt5.14.2/5.14.2/gcc_64/./mkspecs/linux-g++ -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQuick -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQmlModels -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtQml -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtNetwork -isystem /opt/Qt5.14.2/5.14.2/gcc_64/include/QtGui
  OBJECT_DIR = CMakeFiles/PhoneBox.dir
  OBJECT_FILE_DIR = CMakeFiles/PhoneBox.dir/PhoneBox_autogen/EWIEGA46WW


# =============================================================================
# Link build statements for EXECUTABLE target PhoneBox


#############################################
# Link the executable PhoneBox

build PhoneBox: CXX_EXECUTABLE_LINKER__PhoneBox CMakeFiles/PhoneBox.dir/PhoneBox_autogen/mocs_compilation.cpp.o CMakeFiles/PhoneBox.dir/landevicemodel.cpp.o CMakeFiles/PhoneBox.dir/main.cpp.o CMakeFiles/PhoneBox.dir/optimized_video_modifier.cpp.o CMakeFiles/PhoneBox.dir/videoDetect.cpp.o CMakeFiles/PhoneBox.dir/video_generator.cpp.o CMakeFiles/PhoneBox.dir/video_modifier.cpp.o CMakeFiles/PhoneBox.dir/video_normalizer.cpp.o CMakeFiles/PhoneBox.dir/PhoneBox_autogen/EWIEGA46WW/qrc_qml.cpp.o | /opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Quick.so.5.14.2 /opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5QmlModels.so.5.14.2 /opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Qml.so.5.14.2 /opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Network.so.5.14.2 /opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Gui.so.5.14.2 /opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Core.so.5.14.2 || PhoneBox_autogen
  FLAGS = -g
  LINK_LIBRARIES = -Wl,-rpath,/opt/Qt5.14.2/5.14.2/gcc_64/lib  /opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Quick.so.5.14.2  /opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5QmlModels.so.5.14.2  /opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Qml.so.5.14.2  /opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Network.so.5.14.2  /opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Gui.so.5.14.2  /opt/Qt5.14.2/5.14.2/gcc_64/lib/libQt5Core.so.5.14.2
  OBJECT_DIR = CMakeFiles/PhoneBox.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = PhoneBox
  TARGET_PDB = PhoneBox.dbg


#############################################
# Utility command for PhoneBox_autogen

build PhoneBox_autogen: phony CMakeFiles/PhoneBox_autogen PhoneBox_autogen/mocs_compilation.cpp


#############################################
# Custom command for PhoneBox_autogen/EWIEGA46WW/qrc_qml.cpp

build PhoneBox_autogen/EWIEGA46WW/qrc_qml.cpp: CUSTOM_COMMAND ../qml.qrc CMakeFiles/PhoneBox_autogen.dir/AutoRcc_qml_EWIEGA46WW_Info.json ../ConferenceScreen.qml ../VideoScreen.qml ../PhoneBoxApp.qml ../SettingsScreen.qml ../PhoneScreen.qml ../IPScreen.qml ../StatusScreen.qml ../main.qml ../IP_Analog.qml /opt/Qt5.14.2/5.14.2/gcc_64/bin/rcc || PhoneBox_autogen
  COMMAND = cd /root/PhoneBox/build && /usr/bin/cmake -E cmake_autorcc /root/PhoneBox/build/CMakeFiles/PhoneBox_autogen.dir/AutoRcc_qml_EWIEGA46WW_Info.json Debug
  DESC = Automatic RCC for qml.qrc
  restat = 1


#############################################
# Custom command for CMakeFiles/PhoneBox_autogen

build CMakeFiles/PhoneBox_autogen PhoneBox_autogen/mocs_compilation.cpp: CUSTOM_COMMAND
  COMMAND = cd /root/PhoneBox/build && /usr/bin/cmake -E cmake_autogen /root/PhoneBox/build/CMakeFiles/PhoneBox_autogen.dir/AutogenInfo.json Debug
  DESC = Automatic MOC and UIC for target PhoneBox
  restat = 1

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /root/PhoneBox/build

build all: phony PhoneBox

# =============================================================================
# Built-in targets


#############################################
# Make the all target the default.

default all

#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | ../CMakeLists.txt ../qml.qrc /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5/Qt5Config.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QComposePlatformInputContextPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEglFSEmulatorIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEglFSIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEglFSKmsEglDeviceIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEglFSX11IntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEvdevKeyboardPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEvdevMousePlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEvdevTabletPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEvdevTouchScreenPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QGtk3ThemePlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QIbusPlatformInputContextPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QLinuxFbIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalEglIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QVncIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWaylandEglPlatformIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWaylandIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWaylandXCompositeEglPlatformIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWaylandXCompositeGlxPlatformIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QXcbEglIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QXcbGlxIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QXcbIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5NetworkConfig.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5NetworkConfigVersion.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5Network_QConnmanEnginePlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5Network_QGenericEnginePlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5Network_QNetworkManagerEnginePlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5QmlConfig.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5QmlConfigExtras.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5QmlConfigVersion.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QDebugMessageServiceFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QLocalClientConnectionFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebugServerFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebuggerServiceFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlInspectorServiceFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugConnectorFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugServiceFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlPreviewServiceFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlProfilerServiceFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQuickProfilerAdapterFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QTcpServerConnectionFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5QmlModels/Qt5QmlModelsConfig.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5QmlModels/Qt5QmlModelsConfigVersion.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Quick/Qt5QuickConfig.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Quick/Qt5QuickConfigVersion.cmake /usr/share/cmake-3.16/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.16/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /usr/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.16/Modules/CMakeParseArguments.cmake /usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.16/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake-3.16/Modules/Compiler/GNU.cmake /usr/share/cmake-3.16/Modules/Internal/CMakeCheckCompilerFlag.cmake /usr/share/cmake-3.16/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.16/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.16/Modules/Platform/Linux.cmake /usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.16.3/CMakeCXXCompiler.cmake CMakeFiles/3.16.3/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../CMakeLists.txt ../qml.qrc /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5/Qt5Config.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5/Qt5ConfigVersion.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5/Qt5ModuleLocation.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreConfig.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreConfigExtras.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreConfigExtrasMkspecDir.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreConfigVersion.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Core/Qt5CoreMacros.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5GuiConfig.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5GuiConfigExtras.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5GuiConfigVersion.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QComposePlatformInputContextPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEglFSEmulatorIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEglFSIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEglFSKmsEglDeviceIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEglFSX11IntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEvdevKeyboardPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEvdevMousePlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEvdevTabletPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QEvdevTouchScreenPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QGifPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QGtk3ThemePlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QICNSPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QICOPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QIbusPlatformInputContextPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QJpegPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QLinuxFbIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalEglIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QMinimalIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QOffscreenIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgIconPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QSvgPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QTgaPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QTiffPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QTuioTouchPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QVncIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWaylandEglPlatformIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWaylandIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWaylandXCompositeEglPlatformIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWaylandXCompositeGlxPlatformIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWbmpPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWebGLIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QWebpPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QXcbEglIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QXcbGlxIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QXcbIntegrationPlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Gui/Qt5Gui_QXdgDesktopPortalThemePlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5NetworkConfig.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5NetworkConfigVersion.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5Network_QConnmanEnginePlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5Network_QGenericEnginePlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Network/Qt5Network_QNetworkManagerEnginePlugin.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5QmlConfig.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5QmlConfigExtras.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5QmlConfigVersion.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QDebugMessageServiceFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QLocalClientConnectionFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebugServerFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlDebuggerServiceFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlInspectorServiceFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugConnectorFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlNativeDebugServiceFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlPreviewServiceFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQmlProfilerServiceFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QQuickProfilerAdapterFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Qml/Qt5Qml_QTcpServerConnectionFactory.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5QmlModels/Qt5QmlModelsConfig.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5QmlModels/Qt5QmlModelsConfigVersion.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Quick/Qt5QuickConfig.cmake /opt/Qt5.14.2/5.14.2/gcc_64/lib/cmake/Qt5Quick/Qt5QuickConfigVersion.cmake /usr/share/cmake-3.16/Modules/CMakeCXXInformation.cmake /usr/share/cmake-3.16/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake /usr/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake /usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake /usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake /usr/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake /usr/share/cmake-3.16/Modules/CMakeParseArguments.cmake /usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake /usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake /usr/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake /usr/share/cmake-3.16/Modules/Compiler/GNU-CXX.cmake /usr/share/cmake-3.16/Modules/Compiler/GNU.cmake /usr/share/cmake-3.16/Modules/Internal/CMakeCheckCompilerFlag.cmake /usr/share/cmake-3.16/Modules/Platform/Linux-GNU-CXX.cmake /usr/share/cmake-3.16/Modules/Platform/Linux-GNU.cmake /usr/share/cmake-3.16/Modules/Platform/Linux.cmake /usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake CMakeCache.txt CMakeFiles/3.16.3/CMakeCXXCompiler.cmake CMakeFiles/3.16.3/CMakeSystem.cmake: phony


#############################################
# Clean additional files.

build CMakeFiles/clean.additional: CLEAN_ADDITIONAL


#############################################
# Clean all the built files.

build clean: CLEAN CMakeFiles/clean.additional


#############################################
# Print all primary targets available.

build help: HELP

